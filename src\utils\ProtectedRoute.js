"use client";
import { useAuth } from "@/context/AuthContext";
import Cookies from "js-cookie";
import { usePathname, useRouter } from "next/navigation";
import { useEffect } from "react";

// Protected paths matching middleware config
const protectedPaths = ["/profile", "/user-home-screen", "/game-player"];

export default function ProtectedRoute({ children }) {
  const router = useRouter();
  const { isLoggedIn } = useAuth();
  const pathname = usePathname();

  const token = Cookies.get("token");

  const isProtectedRoute = protectedPaths.some((path) => pathname.startsWith(path));

  useEffect(() => {
    if (!isLoggedIn && isProtectedRoute && !token) {
      router.replace("/login");
    }
  }, [isLoggedIn, router, pathname, isProtectedRoute, token]);

  return children;
}
