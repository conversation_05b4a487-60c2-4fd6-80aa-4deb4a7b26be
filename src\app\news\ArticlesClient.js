"use client";
import Loader from "@/components/Loader";
import useScrollAnimation from "@/hooks/useScrollAnimation";
import apiClient from "@/utils/axiosUtil";
import { faSquareUpRight } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";
import Fiks from "../../../public/images/article/fix.png";
import styles from "./styles.module.css";

const ArticlesClient = ({ articles, articleMedia }) => {
  const [articleData, setArticledata] = useState(articles);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setLoading] = useState(false);
  const [triggerAnimation, setTriggerAnimation] = useState(0);

  const t = useTranslations("News");

  useEffect(() => {
    const getPostData = async () => {
      setLoading(true);
      const url = `${process.env.NEXT_PUBLIC_API_URL}/wp-json/wp/v2/news?per_page=10&page=${page}`;
      const response = await apiClient.get(url);
      const result = response.data;

      if (result.length > 0) {
        setArticledata((prevArticles) => [
          ...prevArticles,
          ...result.filter(
            (newArticle) => !prevArticles.some((article) => article.id === newArticle.id)
          ),
        ]);
        setTriggerAnimation((prev) => prev + 1);
      }

      setLoading(false);
      if (result.length < 10) {
        setHasMore(false);
      }
    };

    getPostData();
  }, [page]);

  const setRef = useScrollAnimation(
    {
      duration: 1,
      ease: "power3.out",
      start: "top bottom",
    },
    [triggerAnimation]
  );

  const formattedDate = (dateStr) => {
    const date = new Date(dateStr);
    const options = { year: "numeric", month: "long", day: "numeric" };
    return new Intl.DateTimeFormat("en-US", options).format(date);
  };

  const loadMoreArticles = () => {
    setPage((prevPage) => prevPage + 1);
  };

  const Heading = ({ content }) => {
    return <h3 dangerouslySetInnerHTML={{ __html: content }} />;
  };

  return (
    <div className={styles.articleWrapper}>
      <header className={styles.articleHeader} ref={(el) => setRef(el)}>
        <h1>{t("title")}</h1> {/* Use translation key */}
        <Image
          src={Fiks}
          className={styles.articleHeaderImg}
          alt={t("headerAlt")}
          priority={true}
          quality={85}
        />
      </header>
      {/* <section className={styles.videoContentWrapper} ref={(el) => setRef(el)}>
        <div>
          <iframe
            title={t("videoTitle")}
            className={styles.videoPlyer}
            src="https://www.youtube.com/embed/a7EHVRqeiQI"
          ></iframe>
        </div>
        <div>
          <h1>{t("whatIsSKIDOPass")}</h1>
          <p>{t("passDescription")}</p>
          <button
            className={styles.getPremiumBtn}
            onClick={() =>
              (window.location.href = `${process.env.NEXT_PUBLIC_API_URL}/app/acquisition_home`)
            }
          >
            {t("getPremiumBtn")}
          </button>
        </div>
      </section> */}
      <section className={styles.articleSection} ref={(el) => setRef(el)}>
        <h2>{t("mediaMentionTitle")}</h2>
        <div className={styles.mediaMentionWrapper}>
          {articleMedia.map((mention, index) => (
            <div key={index}>
              <div
                className={styles.mediaMentionCard}
                style={{ backgroundImage: `url(${mention.imageUrl})` }}
              >
                <a
                  href={mention.link}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`${styles.readPopover} noLinkStyle`}
                >
                  {t("read")}{" "}
                  <Image
                    src="/images/article/blackArrow.png"
                    width={20}
                    height={20}
                    alt={t("arrowAlt")}
                  />
                </a>
              </div>
              <p>{mention.description}</p>
            </div>
          ))}
        </div>
      </section>
      <section className={styles.articleSection}>
        <div className={styles.newsWrapper}>
          {articleData.map((item) => (
            <Link
              key={item.id}
              href={{
                pathname: `/news/${item.slug}`,
              }}
              className={styles.noLinkStyle}
            >
              <div className={styles.articleCard} ref={(el) => setRef(el)}>
                <div>
                  <Image
                    alt={item.yoast_head_json?.og_site_name}
                    src={item.yoast_head_json.schema["@graph"][1].url}
                    className={styles.articleImg}
                    width={300}
                    height={240}
                    loading="lazy"
                    quality={85}
                  />
                </div>
                <div>
                  <p className={styles.newsDate}>{formattedDate(item.date_gmt)}</p>
                  <Heading content={item?.title?.rendered} />
                  <p className={styles.newsSubheading}>{item.yoast_head_json.og_description}</p>
                  <p className={styles.readIcon}>
                    {t("read")} <FontAwesomeIcon icon={faSquareUpRight} />
                  </p>
                </div>
              </div>
            </Link>
          ))}
        </div>
        <div className={styles.buttonContainer}>
          {isLoading ? (
            <Loader />
          ) : (
            hasMore && (
              <button className={styles.loadMoreBtn} onClick={loadMoreArticles}>
                {t("loadMoreBtn")}
              </button>
            )
          )}
        </div>
      </section>
    </div>
  );
};

export default ArticlesClient;
