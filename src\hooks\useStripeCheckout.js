import { useState } from "react";
import { loadStripe } from "@stripe/stripe-js";
import apiClient from "@/utils/axiosUtil";
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_KEY);

const useStripeCheckout = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleCheckout = async ({ lang, planID, authToken }) => {
    setLoading(true);
    setError(null);

    try {
      // Get Stripe.js instance
      const stripe = await stripePromise;
      // Call backend to create the Checkout Session
      const url = `${process.env.NEXT_PUBLIC_SUBSSERVICE_BASE_URL}/subscription/session?l=${lang}`;
      const response = await apiClient.post(url, { PlanID: planID, Source: "acquisition" });

      if (!response) {
        const errorDetails = response.data;
        console.error("Server Error:", errorDetails);
        setError(errorDetails);
        return;
      }

      const session = response.data;

      // Redirect to Checkout
      const result = await stripe.redirectToCheckout({
        sessionId: session.session_id,
      });

      if (result.error) {
        console.error("Stripe Checkout Error:", result.error.message);
        setError(result.error.message);
      }
    } catch (err) {
      console.error("Unexpected Error:", err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  return { handleCheckout, loading, error };
};

export default useStripeCheckout;
