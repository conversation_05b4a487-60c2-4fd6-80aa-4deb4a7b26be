"use client";
import { HomeTilesShimmer, UserImgShimmer } from "@/components/HomeScreenShimmer";
import ScreenRotationOverlay from "@/components/prompt/page";
import UserHomeScreenPopup from "@/components/UserHomeScreenPopup";
import { useAuth } from "@/context/AuthContext";
import apiClient from "@/utils/axiosUtil";
import { extractGameData, getLocale } from "@/utils/helperFunctions";
import { trackWebEngageEvent } from "@/utils/webengage";
import { useLocale, useTranslations } from "next-intl";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import styles from "./styles.module.css";

const UserHomeScreen = () => {
  const [isOverlayOpen, setOverlayOpen] = useState(false);
  const [isDark, setDarkTheme] = useState(false);
  const [selectedCard, setSelectedCard] = useState(null);
  const [isLandscape, setIsLandscape] = useState(window.innerWidth > window.innerHeight);
  const [homeScreenData, setHomeScreenData] = useState(null);
  const [playerData, setPlayerData] = useState([]);
  const { isSubscribed } = useAuth();

  const locale = useLocale();
  const t = useTranslations("UserHomeScree");
  const lang = getLocale(locale);
  const router = useRouter();

  if (!isSubscribed) {
    router.replace("/products");
  }
  useEffect(() => {
    trackWebEngageEvent("WebGlHomepageReached");
  }, []);

  const openOverlay = (cardData) => {
    setSelectedCard(cardData);
    setOverlayOpen(true);
  };

  const closeOverlay = () => {
    setOverlayOpen(false);
    setSelectedCard(null);
  };
  const pageRef = useRef();

  const bgImgLight = "/images/webGl/userHomeScreen/bgLight.png";
  const bgImgDark = "/images/webGl/userHomeScreen/bgDark.png";
  const natureTheme = "/images/webGl/userHomeScreen/natureTheme.webp";
  const spaceTheme = "/images/webGl/userHomeScreen/spaceTheme.webp";

  useEffect(() => {
    const footer = document.getElementById("footer");
    if (footer) footer.style.display = "none";
    const checkOrientation = () => {
      setIsLandscape(window.innerWidth > window.innerHeight);
    };

    // Check on mount
    checkOrientation();
    // Add event listener for resize
    window.addEventListener("resize", checkOrientation);
    fetchHomeScreenTiles();
    fetchPlayerData();

    return () => {
      // Clean up the event listener
      window.removeEventListener("resize", checkOrientation);
      if (footer) footer.style.display = "block";
    };
  }, [lang]);

  const fetchHomeScreenTiles = async () => {
    const url = `${process.env.NEXT_PUBLIC_PRODUCTSERVICE_BASE_URL}/homescreen/content`;

    const params = new URLSearchParams({
      gameid: "web",
      grade: "0",
      player_id: localStorage.getItem("playerId"),
      l: lang,
      ram: "65536",
      version: "8.1",
      platform: "ios",
      is_web_enabled: "true",
    });

    try {
      const response = await apiClient.post(`${url}?${params}`, {});
      const data = response.data;
      setHomeScreenData(data);
    } catch (error) {
      console.error("Error fetching home screen data:", error);
    }
  };

  async function fetchPlayerData() {
    const url = `${process.env.NEXT_PUBLIC_USERSERVICE_BASE_URL}/player?language=${lang}&gameid=doctor&version=8.2&platform=iOS&p=unity&sessionID=9a9d4b7b-59e6-48f3-8ec4-8f6cbcaf8d85&appsgroup=False&did=&ornt=landscape`;

    try {
      const response = await apiClient.get(`${url}`);
      const data = response.data;
      setPlayerData(data);
    } catch (error) {
      console.error("Error fetching home screen data:", error);
    }
  }

  if (!isLandscape) {
    return <ScreenRotationOverlay isOpen={true} />;
  }

  const toggleTheme = () => {
    setDarkTheme((prev) => !prev);
  };

  const handleGameClick = () => {
    trackWebEngageEvent("WebGlGameClk", { current_game: homeScreenData.DisplayName });
    router.push(
      `/game-player?gameUrl=${extractGameData(homeScreenData.WebGameUrl).gameUrl}&gameName=${homeScreenData.DisplayName}&gameOrientation=${extractGameData(homeScreenData.WebGameUrl).gameOrientation}`
    );
  };

  return (
    <div
      ref={pageRef}
      className={`${styles.userHomeScreenWrapper} ${isDark ? styles.darkTheme : styles.lightTheme}`}
    >
      <Image
        src={bgImgLight}
        alt="Light Background"
        layout="fill"
        objectFit="cover"
        priority
        className={`${styles.backgroundImage} ${styles.lightBackground}`}
      />
      <Image
        src={bgImgDark}
        alt="Dark Background"
        layout="fill"
        objectFit="cover"
        priority
        className={`${styles.backgroundImage} ${styles.darkBackground}`}
      />
      <div className={styles.contentWrapper}>
        <div className={styles.userProfileWrapper}>
          {playerData?.players?.length > 0 ? (
            <div className={styles.profileIconWrapper}>
              <div>
                <Image
                  src={`/images/webGl/userHomeScreen/homeScreenAvatar/${playerData.players[0].AvatarIndex}.webp`}
                  alt="User Profile"
                  width={80}
                  height={80}
                  className={styles.userAvatarImg}
                />
              </div>
              <div style={{ color: isDark ? "#ffff" : "black" }}>
                {t("Greeting")}
                <Image
                  src="/images/webGl/userHomeScreen/wavingHand.webp"
                  alt="Waving Hand"
                  width={40}
                  height={40}
                  className={styles.wavingHand}
                />
                , {playerData?.players[0].Nickname}
              </div>
            </div>
          ) : (
            <UserImgShimmer />
          )}
          <div className={styles.themeWrapper} onClick={toggleTheme}>
            <div className={styles.themeImageContainer}>
              <Image
                src={spaceTheme}
                className={`${styles.themeImage} ${styles.spaceTheme}`}
                width={115}
                height={65}
                alt="Space theme"
                priority
              />
              <Image
                src={natureTheme}
                className={`${styles.themeImage} ${styles.natureTheme}`}
                width={115}
                height={65}
                alt="Nature theme"
                priority
              />
            </div>
          </div>
        </div>
        <div className={styles.homeScreenTilesWrapper}>
          {homeScreenData?.DisplayName && (
            <div className={styles.homeScreenHeroTile} onClick={handleGameClick}>
              <div className={styles.heroTileLable}>{homeScreenData?.DisplayName}</div>
              <Image
                src={homeScreenData.IconUrl}
                width={420}
                height={420}
                className={styles.heroGameImage}
                alt={homeScreenData?.DisplayName}
              />
              <Image
                src="/images/webGl/userHomeScreen/playBtn.png"
                width={90}
                height={90}
                className={styles.playBtn}
                alt="Play button"
              />
            </div>
          )}
          <div>
            <div className={styles.gamesSectionWrapper}>
              {homeScreenData?.Tiles ? (
                homeScreenData.Tiles.map(
                  (item, index) =>
                    index % 2 === 0 && (
                      <div
                        className={styles.gameThemeCard}
                        onClick={() => openOverlay(item)}
                        key={index}
                      >
                        <Image
                          src={item.IconURL}
                          fill
                          className={styles.gameCardImg}
                          alt={item.TileName}
                        />
                        <div className={styles.labelWrapper}>
                          <span>{item.TileName}</span>
                        </div>
                      </div>
                    )
                )
              ) : (
                <HomeTilesShimmer />
              )}
            </div>
            <div className={styles.gamesSectionWrapper}>
              {homeScreenData?.Tiles ? (
                homeScreenData.Tiles.map(
                  (item, index) =>
                    index % 2 !== 0 && (
                      <div
                        className={styles.gameThemeCard}
                        onClick={() => openOverlay(item)}
                        key={index}
                      >
                        <Image
                          src={item.IconURL}
                          fill
                          className={styles.gameCardImg}
                          alt={item.TileName}
                        />
                        <div className={styles.labelWrapper}>
                          <span>{item.TileName}</span>
                        </div>
                      </div>
                    )
                )
              ) : (
                <HomeTilesShimmer />
              )}
            </div>
          </div>
        </div>
      </div>
      <UserHomeScreenPopup
        isOpen={isOverlayOpen}
        onClose={closeOverlay}
        isDark={isDark}
        cardData={selectedCard}
      />
    </div>
  );
};

export default UserHomeScreen;
