{"HomePage": {"title": "Hello world!", "nurturingGrowth": {"title": {"part1": "Nurturing", "highlight": "Growth", "part2": "Through", "part3": "Fun Learning and Safety"}, "cards": {"card1": "Curiosity & Creativity", "card2": "Confident Learners", "card3": "Active Screen Time"}}, "discoverThemes": {"title": {"part1": "Discover Themes That", "highlight1": "Educate", "and": "and", "highlight2": "Entertain"}}, "reviews": {"title": "Trusted by Parents"}, "socialProof": {"impactMade": "Impact Made", "problemsSolved": "Problems Solved", "happyLearners": {"title": "Happy", "subtitle": "Learners"}, "rating": {"title": "Rating"}}, "awards": {"title": "Awards"}}, "Acquisition": {"Header": "Unlock Your Child’s Learning Potential", "Subheader": "Discover 1000+ Learning Activities for Holistic Growth Engage in Meaningful Screen Time with SKIDOS", "BestValue": "Best Value", "AwardHeader": "Award-winning learning app", "TestimonialHeader": "Trusted by Parents", "Testimionial1": "It's the best doctor game for little ones and it's super good at making learning fun. My 3 year old is getting better at their ABCs and maths while pretending to be a dentist. They're also learning how to take care of their teeth and brush properly. Great job, developers!", "Testimionial1Parent": "<PERSON>, Mother of 3 year old", "Testimionial2": "A fantastic and engaging game for children. They include coding lessons, which is excellent. My 7 year old loves this, and especially enjoyed being able to select puzzles and challenges within her most beloved topics of interest. Highly recommended!", "Testimionial2Parent": "<PERSON><PERSON>, Father of 7 year old", "Testimionial3": "Kids in my school loves to play this game a lot. Further more it enhanced their math. Cool. Need more game like this", "Testimionial3Parent": "<PERSON><PERSON>, Father of 5 year old", "Testimionial4": "My 5 years old loved this pet care game. This virtual game for toddlers is a wonderful preschool learning game. She had so much fun caring for the pets along with learning to trace letters and numbers! 5 stars!", "Testimionial4Parent": "<PERSON><PERSON><PERSON><PERSON>, Father of 5 year old", "Testimionial5": "I babysat my 4- and 6-year-old niece and nephew, and they loved this learning game. I liked that they learned independently, and it was safe for them. I found they offer more such games, so babysitting will be fun!", "Testimionial5Parent": "<PERSON><PERSON><PERSON><PERSON>, Mother of 4 and 6", "LearningSectionHeading": "What’s SKIDOS all about?", "LearningSection1": "Largest collection of educational games", "LearningSection2": "1000+ learning activities", "LearningSection3": "Play to boost emotions and well-being", "LearningSection4": "Learn to read through phonics.", "LearningSection5": "Practice writing with alphabet tracing", "LearningSection6": "Inclusive avatars for all children", "LearningSection7": "Holistic growth for children", "LearningSection8": "Gamified math learning", "ActivateBtn": "Activate", "Monthly": "Monthly", "Yearly": "Yearly", "Quarterly": "Quarterly", "Qaurterly": "Quarterly"}, "SignUp": {"CreateHeading": "Create Account", "CreateLogin": "Already have an account? ", "CreateAccount": "<PERSON><PERSON>", "CreateEmail": "Enter your email address", "CreateAccept": "Accept COPPA & GDPR Commitments of our Data Privacy", "CreateSignUp": "Sign Up", "CreateWrongId": "Oh no! You’ve entered a wrong email id.", "CreateEmailRequired": "Email is required.", "CreateEmailValidMail": "Please enter a valid email address.", "CreateEmailAccept": "You must accept the Data Privacy commitments."}, "Dashboard": {"DashboardPlan": "Enjoying Your Subscription? Save More with Our Annual Plan!", "DashboardInformation": "Account Information", "DashboardSupport": "Support", "DashboardLogout": "Logout", "DashboardMembership": "Membership Information", "DashboardSubscript": "Subscription Plan", "DashboardExpire": "Expire On", "DashboardActive": "Active", "DashboardDownload": "Download the App", "DashboardApp": "App Store", "DashboardPlay": "Play Store", "DashboardExplore": "Explore more", "DashboardInactive": "In Active", "DashboardPlan4": "Enjoying Your Subscription?", "DashboardPla5": "Save More with Our Annual Plan!", "DashboardPlanLine4": "Unlock all with Premium", "DashboardPlanLine5": "Start your 3 day free trial", "DashboardPlanLine1": "Thank You for Committing to", "DashboardPlanLine2": "Learning Fun All Year Round!", "DashboardPlanLine3": "You've Made a Great Decision", "DashboardAnnual": "Activate your Free Trial", "DashboardCancelSubscription": "Cancel subscription"}, "GetStarted": {"Greeting": "Hi, There!", "CarouselSubText1": "Ensure active learning time", "CarouselSubText2": "Spark curiosity and creativity", "CarouselSubText3": "Discover 1000+ activities", "CtaText": "Get Started", "LoginMessage": "Already have an account?", "LoginCta": "<PERSON><PERSON>"}, "CreatePlayer": {"Heading": "Create your child's player", "SubHeading": "Help us curate as per your child's age", "NameLabel": "Name", "NameInputPlaceholder": "Type your name here", "AvatarLabel": "Avatar", "AgeLabel": "Age", "CtaBtn": "Next", "InfoText": "Why do we need this info?", "WhypopupHeading": "Why create your child's player profile?", "PopupItem1": "To personalize our learning content and games based on your child's age.", "PopupItem2": "To have your own unique digital representation with inclusive avatars.", "PopupItem3": "To ensure personal expression and a sense of belongingness.", "PopCta": "Okay", "ErrorMsgAll": "All fields are mandatory."}, "PersonalisePlayer": {"Heading": "Personalize your child's experience", "SubHeading": "Select one or more interests", "CtaBtn": "Next", "InfoText": "Why do we need this info?", "WhypopupHeading": "Why personalize your child's experience?", "PopupItem1": "To ensure tailored gamified learning experience.", "PopupItem2": "To ensure age appropirate display of learning content.", "PopCta": "Okay", "ErrorMsgAll": "Please select one or more to continue."}, "EnterEmailSection": {"Heading": "Create an account", "SubHeading1": "To track your child’s learning progress", "SubHeading2": "To seamlessly login across devices", "SubHeading3": "To send encouraging messages to your child", "EmailPlaceholder": "Type your email here", "Checkbox": "Accept COPPA & GDPR commitments on our", "CheckboxDataPrivacy": "Data Privacy", "CtaBtn": "Next", "InfoText": "Why do we need this info?", "WhypopupHeading": "Why do we need your email?", "PopupItem1": "To provide information about your child's progress via email, keeping you updated and involved in their learning journey.", "PopupItem2": "To create a unique ID that ensures secure access to your SKIDOS account.", "PopupItem3": "To facilitate effective communication between parents and our educators, allowing for easy exchanges of feedback, questions, and updates.", "PopCta": "Okay", "LoginCta": "Please Login", "EmailError": "Oh no ! You’ve entered a wrong Email", "ErrorCheckbox": "Check Data Privacy box to continue", "InvalidEmailKickbox": "The email address you entered is not valid. Please enter a valid email."}, "Purchase": {"Heading": "Get SKIDOS Pass", "SubHeading1": "Access 1000+ learning activities", "SubHeading2": "Personalized gamified experience", "CtaBtn": "Start your 3 day free trial"}, "LoginEmail": {"Heading": "Enter your email & Password", "InputPlaceholder": "Type your email here", "PasswordPlaceholder": "Type your password here", "CtaBtn": "<PERSON><PERSON>", "ForgetPassCta": "Forgot Password? Login via Verification code", "EmailIncorrect": "Your email is incorrect.Please try again", "PasswordIncorrect": "Your password is incorrect. Please try again", "AllMandotry": "All fields are mandatory."}, "LoginOtp": {"Heading": "Enter your email", "Subheading": "We will send you a verification code to ensure succesful login", "InputPlaceholder": "Type your email here", "CtaBtnGetCode": "Get Code", "EmailIncorrect": "Oh no ! You’ve entered a wrong Email", "CtaLogin": "Log In", "HeadingOtp": "Check your email", "SubheadingOtp": "Enter the code sent to", "OtpError": "You have entered a wrong verification code. Please try again.", "OtpSent": "Verification code sent to your email.", "OtpRegenerated": "A new verification code has been sent to your email.", "OtpVerified": "Verification successful!", "OperationFailed": "Operation failed. Please try again later.", "ErrorOccurred": "An unexpected error occurred. Please try again.", "ResendTimer": "Resend in {timer} sec.", "Resend": "Resend", "Resent": "Resent"}, "UserHomeScree": {"Greeting": "Hello"}, "Otp": {"EnterOtp": "Enter OTP", "OtpAccount": "Don’t have an account? Create Account", "OtpRequest": "Request again in ", "OtpIncorrect": "Incorrect OTP"}, "Footer": {"UnlockGrowth": "Unlock Growth", "UnlockNow": "Unlock Now 🔥", "LearnToRead": "<PERSON><PERSON>", "Tracing": "Tracing", "Fitness": "Fitness", "Activities": "1000+ Activities", "EmotionalWellbeing": "Emotional Well-being", "Math": "Math", "Avatars": "Avatars", "AboutUs": "About Us", "Blogs": "Blogs", "Careers": "Careers", "Support": "Support", "TermsAndConditions": "T&C", "PrivacyPolicy": "Privacy Policy", "FAQ": "FAQ", "Partnership": "Partnership", "Contact": "Contact", "Address": "Skidos Labs ApS,\nTitangade 11\n2200 København N\nCVR: ********", "SupportEmail": "<EMAIL>", "Amazon": "Amazon", "Appstore": "Appstore", "Playstore": "Playstore", "Web": "Web", "News": "News"}, "AcquisitionSuccess": {"PaymentSuccessful": "Payment Successful", "WelcomeMessage": "Welcome to SKIDOS", "LearnMessage": "Learn with 1000+ activities to turn playtime into a boost for your child's creativity, curiosity, and growth.", "LoginInstruction": "Log in using the email and password sent to your inbox.", "GoToHomeButton": "Go to Home"}, "NavbarHome": {"LogoAlt": "SKIDOS Logo", "CloseAlt": "Close Menu", "Home": "Home", "AboutUs": "About Us", "Products": "Products", "Profile": "Profile", "Login": "<PERSON><PERSON>", "FreeTrial": "Free Trial"}, "Carousel": {"slides": {"slide1": {"heading": "Ready to Learn, Ready to Play!", "subheading": "Click, Play, Learn—Instant Fun with SKIDOS! Boost your child's skills with active learning in every game."}, "slide2": {"heading": "Safe and meaningful screen time for holistic growth", "subheading": "Turn screen time into a fun, and purposeful experience that boosts your child's learning, creativity, and growth!"}, "slide3": {"heading": "Active Learning Time", "subheading": "Turn screen time into active learning time with engaging, gamified, and personalized learning that helps your child learn and grow!"}, "slide4": {"heading": "Access to 1000+ learning activites", "subheading": "Unlock 1,000+ personalized games and activities tailored to your child's interests in math, tracing, reading, emotional well-being, and fitness!"}, "slide5": {"ctaButtonHeading": "Read Study", "heading": "Learning That's Backed by Evidence", "subheading": "51% Accuracy Boost. 39% Stronger Performance. See what SKIDOS can do for your child"}, "slide6": {"ctaButtonHeading": "Know More", "heading": "Coming Soon: Smurfs Playhouse", "subheading": "Get ready for a magical world of pretend play! Explore interactive rooms and create stories with your favourite Smurfs, only with SKIDOS."}}, "buttons": {"startLearning": "Start Learning"}, "imageAlts": {"childParent": "Child Parent"}}, "AboutPage": {"meta": {"title": "About SKIDOS | Active Learning Time for Kids", "description": "Learn more about SKIDOS, our mission to turn passive screen time into active learning time by creating engaging educational games for kids, and our commitment to making learning fun for children aged 2-10."}, "mission": {"title": {"our": "Our", "highlight": "Mission"}, "arrowAlt": "Arrow", "description": "<PERSON><PERSON> turns passive screen time into active learning time by creating and providing a world of edutainment for young minds, that is playful, creative and physically engaging."}, "history": {"title": "History", "imageAlt": "About Us Image"}, "gang": {"title": "Meet SKIDOS Gang", "moyo": {"name": "<PERSON><PERSON>", "description": "Meet <PERSON><PERSON>, an artistic yellow owl who excels in painting, drawing, sculpting, and filmmaking. With his big imagination and focus, <PERSON><PERSON> is eager to help you explore your talents and guide you on your creative journey!"}, "fiks": {"name": "Fiks", "description": "<PERSON> <PERSON><PERSON>, a brainy and curious bluebird who loves reading, experimenting, and creating new inventions. Jo<PERSON> in SKIDOS apps to explore math and logic tasks, and tackle learning challenges together!"}, "neta": {"name": "<PERSON><PERSON>", "description": "Meet <PERSON><PERSON>, a friendly and adventurous red duck who loves sports and fun games. With <PERSON><PERSON>'s curiosity and positive attitude, you'll always find exciting ways to learn and stay motivated!"}}}, "News": {"headerAlt": "Fiks Reading", "videoTitle": "intro_video", "whatIsSKIDOPass": "What Is The SKIDOS Pass?", "passDescription": "SKIDOS Pass is your child’s gateway to games that combine learning with endless fun! Different from traditional learning apps, SKIDOS Pass lets your child play 40+ popular and exciting games with 1000+ learning activities inside to develop their 21st-century skills.", "getPremiumBtn": "Get Premium", "read": "Read", "arrowAlt": "Arrow", "loadMoreBtn": "Load More", "teamSkidos": "Team SKIDOS", "readingTime": "Reading Time", "postNotFound": "Post Not Found", "title": "News", "mediaMentionTitle": "Media Mentions"}, "careers": {"title": "Careers at SKIDOS– Join Our Team and Make an Impact", "meta_description": "Discover exciting career opportunities at Skidos. Join our team and enjoy outstanding benefits while innovating the future of children's education.", "job_available": "Job Available", "apply_now": "Apply Now", "join_us": "Join us to Inspire, Innovate, and Impact", "jobs_coming_soon": "Jobs coming soon... Stay tuned on LinkedIn!", "company_mission": "Join <PERSON> to help achieve our vision to develop a future where every child unlocks their full intellectual and physical potential...", "explore_perks": "Explore Our Perks & Benefits", "health_coverage": "Health Coverage", "health_description": "Inclusive health insurance plans for your well-being", "parental_leave": "<PERSON><PERSON>", "parental_description": "Supportive maternity and paternity leave for life’s precious moments.", "wellness": "Wellness Initiatives", "wellness_description": "Free healthy snacks to promote overall well-being.", "growth_opportunities": "Growth Opportunities", "growth_description": "Dedicated learning and development programmes to enhance your skills.", "leave_policy": "Flexible Leave Policy", "leave_description": "Adaptable leave structure to suit your personal and professional needs.", "team_engagement": "Team Engagement Activities", "engagement_description": "Exciting team-building outings to foster collabouration and camaraderie.", "collaborative_culture": "Collabourative Culture", "collaborative_description": "A transparent, supportive team dynamic.", "tax_assistance": "Tax Planning Assistance", "tax_description": "Support for effectively optimizing tax outflows."}, "ContactUs": {"title": "Contact SKIDOS | Get in Touch for Support & Inquiries", "metaDescription": "Reach out to SKIDOS for customer support, inquiries, or partnership opportunities. We are here to help with any questions you have about our educational games.", "header": "How can we help you?", "addressLabel": "Address", "address": "Skidos Labs ApS, Titangade 11 2200\nKøbenhavn N CVR: ********", "emailLabel": "Email", "email": "<EMAIL>", "mascotsAlt": "Mascots", "formTitle": "Contact Us", "form": {"firstName": "First Name", "email": "Email", "selectCategory": "Select category", "general": "General", "support": "Support", "sales": "Sales", "message": "Message", "privacyPolicy": "I accept", "privacyLink": "Privacy Policy", "submit": "Submit"}}, "Partnership": {"title": "SKIDOS Partnership programme", "subtitle": {"mobile": "Enhancing Learning Through Meaningful Active Screen Time", "desktop1": "Enhancing Learning Through Meaningful", "desktop2": "Active Screen Time"}, "collaborate": "Let's Collabourate", "mascotsAlt": "Mascots", "ourPartners": "Our Partners", "mitgameAlt": "Mitgame", "prodegeAlt": "Prodege", "waysCollaborate": "Ways in which we collabourate", "brandPartnership": "Brand Partnership", "brandPartnershipAlt": "Hand Shake", "contentLicensing": "Content Licensing", "contentLicensingAlt": "Certificate", "csr": "Corporate Social Responsibility", "csrAlt": "Heart"}, "PrivacyPolicy": {"title": "Privacy Policy", "introduction": {"heading": "Introduction:", "description": "SKIDOS is a platform that offers 70+ learning game apps for kids on Appstore and Playstore. At SKIDOS Labs (collectively referred to below as \"skidos\", \"skidos learning\" \"we\", \"us\" or \"our\"), we understand that our apps are used and loved by families with children, and we are committed to keeping their data and privacy protected.", "policyDescription": "This Privacy Policy describes the information collected by our mobile applications and educational games and website and how we use that information. We encourage you to read the Privacy Policy carefully, and if you have any questions, please contact us as set forth in \"Contact Us\" below."}, "information": {"heading": "We Collect Limited Information With Our Applications.", "description": "When you download and use our mobile applications, we don't collect any information about you or your device except the following:", "optIn": {"title": "Opt-in Account Information:", "description": "We may collect the information that you directly share with us to create your SKIDOS account. This includes your email address and other information only as permitted by you. This information helps us serve you better by providing regular updates of your children or student's reports and usage of our apps, to access important statistics of your children's learning performance within the app, with your email addresses used solely to send reports, newsletters and promotional updates. Other than this use of your email address, your information is never stored, shared or disclosed, and is used solely to provide the features within those applications."}, "nonPersonal": {"title": "Non-personal information", "description": "about the duration the mobile application is used and how it is used, such as the screens viewed and actions taken within the mobile applications, which is used solely to enhance and improve our applications."}, "appCrash": {"title": "App crash reports –", "description": "Debug information that will help us identify and resolve the root cause of any crashes that make it to a commercial build."}, "installs": {"title": "Installs –", "description": "We work with partners to learn how people find our apps. This means that when you download our apps, we may learn that you came from a certain website (a review site, a web advertisement, our company web page, etc). We may also collect a Facebook token if you are a Facebook user, but only to see whether folks installed our app from one of our Facebook ad campaigns. We track installations to understand how we can introduce more parents to our products, and we make sure that our partners never use your personal information for any other purpose."}}, "pushNotifications": {"title": "Push Notifications", "description": "For our iOS applications, we may send push notifications if the user opts in to receive them. Apple's push notification process automatically provides us with a token which is identified to a specific device, enabling notifications to be sent to that device through Apple's push notification service. The token is used only to send push notifications. The token is not disclosed and is not used for any other purpose or combined with any other information. We send push notifications to communicate information about how to interact with our applications. Notifications can be turned off through the \"Notifications\" tab within iOS \"Settings.\""}, "cookies": {"title": "Cookies", "introduction": "SKIDOS uses cookies Din order to personalize your navigation, as well as to protect and improve the website. Please read this Cookie Policy carefully to learn about SKIDOS use of cookies and the options you have to configure your browser to manage them.", "usage": "Through the use of cookies it is possible that the server where the website is hosted recognizes the web browser used by the user, in order to make navigation easier, as well as to measure the audience and traffic parametres or to control the progress and number of entries.", "changes": {"title": "Changes and updates to the cookie policy", "description": "SKIDOS may modify this Cookie Policy according to legislative or regulatory requirements, or in order to adapt this policy to the instructions and recommendations issued by the regulators. It is therefore recommended that you periodically review this Cookie Policy."}, "whatAre": {"title": "What are cookies?", "description": "Cookies are small data files that are generated on the user's computer and allow us to know the frequency of visits, the most selected content and security elements that may be involved in controlling access to restricted areas, and that are activated by cookies served by SKIDOS or by third parties that provide these services on its behalf."}, "table": {"headers": {"name": "<PERSON><PERSON>", "description": "Description", "provider": "Provider"}, "ga": {"description": "This cookie is used to distinguish unique users by assigning a randomly generated number as a client identifier."}}}, "contact": {"title": "Contact Us", "description": "If you have any questions or concerns regarding this Privacy Policy, please send us an email at, or raise a ticket at https://support.skidos.com/", "mailing": {"title": "Mailing address", "address": "Skidos Labs ApS,\nTitangade 11\n2200 København N\nCVR: 372129"}}}, "Support": {"header": {"title": "How can we help you ?", "addressLabel": "Address", "address": "Skidos Labs ApS, Titangade 11 2200\nKøbenhavn N CVR: ********", "emailLabel": "Email", "email": "<EMAIL>", "mascotAlt": "Mascots"}, "faq": {"title": "FAQ"}, "form": {"title": "Raise a Ticket", "emailPlaceholder": "Email", "messagePlaceholder": "Explain where you need help", "attachment": "Attachment", "submitButton": "Raise a ticket"}, "links": {"title": "Links", "termsOfService": "Terms of Service"}}, "Carousel1": {"common": {"getStarted": "Get Started"}, "slides": {"math": {"title": "Math", "description": "Engaging Math Games to Build Strong Skills", "imageAlt": "Math"}, "avatars": {"title": "Avatars", "description": "Personalize Learning with Diverse, Inclusive Avatars", "imageAlt": "Avatar"}, "reading": {"title": "Learn to Read", "description": "Interactive and Engaging Activities to Foster Early Reading", "imageAlt": "Learn to Read"}, "emotional": {"title": "Emotional Wellbeing", "description": "Games to Support Emotional Health and Resilience", "imageAlt": "Emotional Wellbeing"}, "tracing": {"title": "Tracing", "description": "Fun Tracing Activities for Writing practise", "imageAlt": "Tracing"}}}, "blog": {"essential_reads": "Essential Reads for Growing Your Child's Potential!", "reading_benefits": "Discover how reading shapes your child’s future! Our article explores the science behind reading comprehension and its impact on development. Packed with tips, it's a must-read for giving your child a head start."}, "errorPopup": {"login_failed": "Login Failed", "incorrect_credentials": "Your email Id and password are incorrect. Please try again.", "ok": "Ok", "forgot_password": "Forgot Password? Login Via verification Code"}, "languageSelector": {"translator_alt": "Language Translator", "menu_selector_alt": "<PERSON><PERSON> Selector", "english_us": "English (US)", "english_uk": "English (UK)", "danish": "Danish", "portuguese": "Portuguese", "norwegian": "Norwegian", "swedish": "Swedish"}, "terms": {"title": "SKIDOS Terms | Terms of Service", "pageTitle": "Terms of Service", "lastUpdated": "Hello! These are SKIDOS Terms of service. They were last updated on April 1st, 2019", "ageWarning": "Children, if you have not reached the age of majority in your jurisdiction, you must have a parent or legal guardian read and accept these terms of service on your behalf and take full responsibility for compliance with these terms of service.", "accessAcknowledge": "By accessing a SKIDOS service, downloading a SKIDOS application, or continuing to use the service, you acknowledge that: (1) you are at least 18 year of age or any older legal age required to form a contract in your jurisdiction; (2) you have the right, authority and legal capacity to enter into this agreement; (3) you have read, understood and agree to be bound by these terms with respect to yourself and any minor child authorised by you.", "contact": "Contact <NAME_EMAIL> in case of anything you do not understand.", "exitWarning": "IF YOU OR YOUR PARENT/LEGAL GUAR<PERSON>AN DO NOT WISH TO BE BOUND BY THE THESE TERMS OF SERVICE, PLEASE EXIT THE WEBSITE NOW AND DO NOT ACCESS OR USE SKIDOS SERVICES OR ANY SKIDOS APPLICATIONS. YOUR AGREEMENT WITH US REGARDING COMP<PERSON><PERSON><PERSON>E WITH THESE TERMS OF SERVICE BECOMES EFFECTIVE IMMEDIATELY UPON COMMENCEMENT OF USE OF THIS SERVICE INCLUDING VISITING www.skidos.com OR", "sections": {"acknowledgment": {"title": "1. User's Acknowledgment and Acceptance of Terms of Service", "content": "SKIDOS ('SKIDOS') provides www.skidos.com (the 'Website') and various related educational games, applications, features, content, plug-ins, widgets, downloads, or services (collectively the 'Service') to you, the user, subject to your compliance with these Terms of Service, as well as any other written agreement between us and you."}, "userInfo": {"title": "2. User Information", "content": "SKIDOS's collection, use and disclosure, if any, of information collected from you or a child authorised by you is detailed in the SKIDOS Privacy Policy (published at www.skidos.com/privacy-policy ), which is incorporated by reference and made a part of these Terms of Service."}, "userAccount": {"title": "3. User Account", "content": "YOU <PERSON><PERSON><PERSON><PERSON><PERSON>DGE AND AGREE THAT <PERSON><PERSON>ULD SKIDOS PROVIDE THE ABILITY TO CREATE A USER ACCOUNT, YOU SHALL HAVE NO OWNERSHIP OR OTHER PROPERTY INTEREST IN ANY ACCOUNT OR USER PROFILE, AND YOU FURTHER ACKNOWLEDGE AND AGREE THAT <PERSON><PERSON> RIGHTS IN AND TO ANY ACCOUNT OR USER PROFILE ARE AND SHALL FOREVER BE OWNED BY AND INURE TO THE BENEFIT OF SKIDOS."}, "conduct": {"title": "4. Conduct Through the Service", "content": "You are solely responsible for your conduct through the Service and agree that you will not:"}, "intellectualProperty": {"title": "5. Intellectual Property Information", "content": "Content. The Service contains a variety of content including, without limitation: (i) information, videos, photos, graphics, music, sounds, text, data, communications, illustrations, documentation, and other material and services that users can view on, access through, or contribute to the Service."}, "charges": {"title": "6. Charges and Billing", "content": "The Service is an online system that may allow parent-authorised purchases funded by a credit card or other payment method from participating merchants using the Service."}}, "refundPolicy": {"title": "21. Refund and Cancellation Policy", "applicability": "(Applicable solely for subscriptions purchased via skidos.com)", "points": {"autoRenewal": "Subscriptions purchased will automatically renew unless turned off atleast 24 hours before the end of current billing period. User will have complete and unrestricted access to subscribed content till the end of the current billing period.", "refundRequest": "User may request for a refund by <NAME_EMAIL> within 3 days of the date of purchase, specifying the reason for cancellation and attaching the payment receipt as proof.", "discretion": "Please note that a request for refund is subject to management discretion.", "eligibility": "No request for refund shall be eligible after 3 days from the date of the transaction. Refunds will not be provided for subscriptions that are cancelled after the trial period has ended. or after auto-renewal.", "refundMethod": "Refunds granted will be applied to the original payment option only."}}, "effectiveDate": "Effective Date: November 9, 2015"}, "PrivacyPage": {"metadata": {"title": "SKIDOS Privacy Policy"}, "title": "Privacy Policy", "introduction": {"paragraph1": "SKIDOS is a platform that offers 70+ learning game apps for kids on Appstore and Playstore. At SKIDOS Labs (collectively referred to below as \"skidos\", \"skidos learning\"  \"we\", \"us\" or \"our\"), we understand that our apps are used and loved by families with children, and we are committed to keeping their data and privacy protected.", "paragraph2": "This Privacy Policy describes the information collected by our mobile applications and educational games and website and how we use that information. We encourage you to read the Privacy Policy carefully, and if you have any questions, please contact us as set forth in \"Contact Us\" below."}, "limitedCollection": {"heading": "We Collect Limited Information With Our Applications.", "description": "When you download and use our mobile applications, we don't collect any information about you or your device except the following:", "optInAccount": {"title": "Opt-in Account Information", "description": "We may collect the information that you directly share with us to create your SKIDOS account. This includes your email address and other information only as permitted by you. This information helps us serve you better by providing regular updates of your children or student's reports and usage of our apps, to access important statistics of your children's learning performance within the app, with your email addresses used solely to send reports, newsletters and promotional updates. Other than this use of your email address, your information is never stored, shared or disclosed, and is used solely to provide the features within those applications."}, "nonPersonalInfo": {"title": "Non-personal information", "description": "about the duration the mobile application is used and how it is used, such as the screens viewed and actions taken within the mobile applications, which is used solely to enhance and improve our applications. We use a variety of third-party SDKs for the same, as listed here: WebEngage SDK (read privacy policy); Embrace SDK (read privacy policy); AppsFlyer SDK (read security policy); and Google Firebase SDK (read privacy policy)."}, "crashReports": {"title": "App crash reports", "description": "Debug information that will help us identify and resolve the root cause of any crashes that make it to a commercial build."}, "installs": {"title": "Installs", "description": "We work with partners to learn how people find our apps. This means that when you download our apps, we may learn that you came from a certain website (a review site, a web advertisement, our company web page, etc). We may also collect a Facebook token if you are a Facebook user, but only to see whether folks installed our app from one of our Facebook ad campaigns. We track installations to understand how we can introduce more parents to our products, and we make sure that our partners never use your personal information for any other purpose."}}, "pushNotifications": {"title": "Push Notifications", "description": "For our iOS applications, we may send push notifications if the user opts in to receive them. Apple's push notification process automatically provides us with a token which is identified to a specific device, enabling notifications to be sent to that device through Apple's push notification service. The token is used only to send push notifications. The token is not disclosed and is not used for any other purpose or combined with any other information. We send push notifications to communicate information about how to interact with our applications. Notifications can be turned off through the \"Notifications\" tab within iOS \"Settings.\""}, "cookies": {"title": "Cookies", "description": "SKIDOS uses cookies in order to personalize your navigation, as well as to protect and improve the website. Please read this Cookie Policy carefully to learn about SKIDOS use of cookies and the options you have to configure your browser to manage them."}, "complianceSection": {"coppaTitle": "We Comply with the Children's Online Privacy Protection Act", "coppaDescription1": "Our apps are intended for children and we do not knowingly collect any personal information from children.", "coppaDescription2": "Our mobile applications comply with the Children's Online Privacy Protection Act (\"COPPA\"). We don't knowingly collect personal information from children under the age of 13 in violation of COPPA, and if in the event that a user identifies himself or herself as a child under the age of 13 through a support request, we will not collect, store or use, and will delete in a secure manner, any personal information of such user."}, "contact": {"title": "Contact Us", "supportEmail": "<EMAIL>", "supportLink": "https://support.skidos.com/", "mailingAddress": {"company": "Skidos Labs ApS", "street": "Titangade 11, SingularityU", "city": "2200, Copenhagen, Denmark", "cvr": "CVR: 372129"}}}, "SmurfsPage": {"metadata": {"title": "SKIDOS x Smurfs", "description": "Smurfs Are Here to Learn & Play! SKIDOS and the Smurfs have teamed up to bring exciting educational games."}, "bannerAlt": "Smurf Characters", "mainHeading": "Welcome to the Smurfs Playhouse!", "subHeading": "(A Smurftastic Blend of Imagination, Learning & Play)", "description": "Get ready for a smurfy new adventure! SKIDOS brings your little ones an all-new interactive pretend play experience featuring their favourite blue friends, perfectly designed for kids aged 3 to 7 years. From the playroom to the playground, the kitchen, bathroom and beyond, each area is filled with smurfalicious surprises and interactive fun. Kids can tap, drag and create endless stories with beloved characters like <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> Smurf and more. Whether it's dress-up time, cooking or a cozy bedtime routine, every scene is a chance for storytelling and imaginative discovery. Let your child's imagination run smurfwild!", "gameCards": {"card1": "THE CHARACTERS YOU LOVE!", "card2": "WATCH OUT FOR GARGAMEL!", "card3": "PLAY MINI GAMES", "card4": "MAGICAL ADVENTURES!", "card5": "LEARN WITH SMURFS!", "card6": "SMURF VILLAGE!", "card7": "SMURF VILLAGE!"}, "Appstore": "Pre-order on the", "gameScreenshotAlt": "Smurf Game Screenshot"}, "EmotionalWellBeingPage": {"metadata": {"title": "Emotional Well-being | SKIDOS Learning Games", "description": "A mindful game designed by teachers to boost your child's emotional growth and learning through interactive stories, quizzes, and games."}, "hero": {"subtitle": "A mindful game designed by teachers to boost your child's emotional growth and learning", "title": "Emotional Wellbeing"}, "videoPlayer": {"loadingText": "Loading video player..."}, "sections": {"whyPlay": {"title": "Why play Emotional Well-being?", "description": "Our digital learning experience takes children on a playful deep-dive into their emotions. This delightful interactive game nurtures emotional well-being by helping kids build strong relationships, manage stress, and develop a positive, confident personality", "imageAlt": "Why Play Rekindle Minds"}, "playtimeWithPurpose": {"title": "Playtime with a purpose", "description": "Explore, learn and grow with 150+ immersive stories, quizzes, puzzles, and games - designed by trusted educators to develop your child's EQ", "imageAlt": "How it Works"}, "expertApproved": {"description": "Lay a strong foundation for social-emotional learning with 5 playful islands designed as per the CASEL framework", "imageAlt": "Progress Report", "title": "Expert-approved guide to Emotional Wellbeing"}}}, "LearnToReadPage": {"metadata": {"title": "Learn to Read | SKIDOS Learning Games", "description": "An exciting journey to develop early reading skills through interactive games and activities aligned with Science of Reading."}, "hero": {"title": "Learn to Read", "subtitle": "An exciting journey to develop early reading skills"}, "videoPlayer": {"loadingText": "Loading video player..."}, "sections": {"readingAdventure": {"title": "Join the Reading Adventure", "description": "Learn letters, phonics, and first words on a delightful treasure hunt!", "imageAlt": "Join Reading Adventure"}, "scienceOfReading": {"title": "Aligned with the Science of Reading (SoR)", "description": "Develop reading fluency and confidence with 20+ expert-approved learning activities", "imageAlt": "Science Of Reading"}}}}