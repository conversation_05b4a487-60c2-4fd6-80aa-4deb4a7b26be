import { notFound } from "next/navigation";
import { gamesData } from "@/constants";
import { findGameBySlug, generateGameSlug } from "@/utils/helperFunctions";
import ProductPageDetails from "./ProductPageDetails";

// Generate metadata for SEO
export async function generateMetadata({ params }) {
  const { slug } = params;
  const game = findGameBySlug(gamesData, slug);

  if (!game) {
    return {
      title: "Product Not Found | SKIDOS",
      description: "The requested product could not be found.",
    };
  }

  return {
    title: `${game.gameName} | SKIDOS Educational Games`,
    description: game.gameDesc,
    openGraph: {
      title: `${game.gameName} | SKIDOS Educational Games`,
      description: game.gameDesc,
      type: "website",
      images: [
        {
          url: game.gameThumbnailImg?.src || "/images/default-game-thumbnail.webp",
          width: 1200,
          height: 630,
          alt: game.gameName,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: `${game.gameName} | SKIDOS Educational Games`,
      description: game.gameDesc,
      images: [game.gameThumbnailImg?.src || "/images/default-game-thumbnail.webp"],
    },
    alternates: {
      canonical: `https://skidos.com/products/${slug}`,
    },
  };
}

// Generate static params for all games (optional - for static generation)
export async function generateStaticParams() {
  return gamesData.map((game) => ({
    slug: generateGameSlug(game.gameDesc),
  }));
}

// Structured data for SEO
const ProductStructuredData = ({ game }) => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Product",
    name: game.gameName,
    description: game.gameDesc,
    image: game.gameThumbnailImg?.src || "/images/default-game-thumbnail.webp",
    brand: {
      "@type": "Brand",
      name: "SKIDOS",
    },
    offers: {
      "@type": "Offer",
      availability: "https://schema.org/InStock",
      price: "0",
      priceCurrency: "USD",
      url: `https://skidos.com/products/${generateGameSlug(game.gameDesc)}`,
    },
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: "4.0",
      reviewCount: "100",
    },
    category: game.gameSkill,
    keywords: game.interests?.join(", "),
  };

  return <script type="application/ld+json">{JSON.stringify(structuredData)}</script>;
};

const ProductPage = ({ params }) => {
  const { slug } = params;
  const game = findGameBySlug(gamesData, slug);

  if (!game) {
    notFound();
  }

  return (
    <>
      <ProductStructuredData game={game} />
      <ProductPageDetails game={game} />
    </>
  );
};

export default ProductPage;
