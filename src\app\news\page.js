import { ArticleMedia } from "@/constants";
import ArticlesClient from "./ArticlesClient.js"; // Client Component

export const metadata = {
  title: "SKIDOS News | Latest News, Media Coverage & Announcements",
  description:
    "Stay updated with the latest news, media coverage, and press releases from SKIDOS. Discover how we are revolutionizing kids' intellectual and physical development through our educational games.",
  openGraph: {
    title: "SKIDOS News & Media Coverage",
    description: "Latest news, updates and media coverage from SKIDOS - Educational Games for Kids",
    type: "website",
    url: "https://skidos.com/news/",
  },
  alternates: {
    canonical: "https://skidos.com/news/",
  },
};

const ArticleList = async () => {
  const fetchArticles = async (page) => {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/wp-json/wp/v2/news?per_page=10&page=${page}`,
      {
        next: { revalidate: 3600 }, // Cache for 1 hour
      }
    );

    const data = await response.json();
    return data;
  };

  const articles = await fetchArticles(1);

  return <ArticlesClient articles={articles} articleMedia={ArticleMedia} />;
};

export default ArticleList;
