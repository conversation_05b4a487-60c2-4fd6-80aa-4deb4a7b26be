import { useTranslations } from "next-intl";
import styles from "./styles.module.css";

export default function ErrorPopup({ isVisible, onClose, erroMessage, redirectHanlder }) {
  const t = useTranslations("errorPopup");

  if (!isVisible) return null;

  return (
    <div className={styles.overlay}>
      <div className={styles.popup}>
        <h2 className={styles.title}>{t("login_failed")}</h2>

        <p className={styles.message}>{erroMessage ?? t("incorrect_credentials")}</p>

        <button className={styles.button} onClick={onClose}>
          {t("ok")}
        </button>

        <a onClick={redirectHanlder} className={styles.link}>
          {t("forgot_password")}
        </a>
      </div>
    </div>
  );
}
