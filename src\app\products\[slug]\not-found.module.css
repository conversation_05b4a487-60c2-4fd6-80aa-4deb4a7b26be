.notFoundWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 40px 20px;
  font-family: var(--font-poppins);
}

.notFoundContent {
  text-align: center;
  max-width: 500px;
}

.notFoundTitle {
  font-size: 2.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}

.notFoundDescription {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 40px;
  line-height: 1.5;
}

.notFoundActions {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.backToProductsBtn,
.homeBtn {
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s;
}

.backToProductsBtn {
  background-color: #1f5dfe;
  color: white;
}

.backToProductsBtn:hover {
  background-color: #1a4fd6;
}

.homeBtn {
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #ddd;
}

.homeBtn:hover {
  background-color: #e0e0e0;
}

@media (max-width: 480px) {
  .notFoundTitle {
    font-size: 2rem;
  }
  
  .notFoundActions {
    flex-direction: column;
    align-items: center;
  }
  
  .backToProductsBtn,
  .homeBtn {
    width: 200px;
  }
}
