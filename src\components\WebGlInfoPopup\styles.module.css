/* Overlay background */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.popup {
  background-color: #fdf8f5;
  max-width: 400px;
  border-radius: 15px;
  padding: 20px;
  text-align: left;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  transform: translateY(100%);
  opacity: 0;
  transition:
    transform 0.4s ease,
    opacity 0.3s ease;
}

.popupVisible {
  transform: translateY(0);
  opacity: 1;
}

.popupHidden {
  transform: translateY(100%);
  opacity: 0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Remove the old slideDown animation */

/* Close button */
.closeButton {
  position: absolute;
  top: 15px;
  left: 15px;
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
}

/* Image styling */
.image {
  width: 100%;
  height: auto;
  border-radius: 10px;
  margin-bottom: 0.5rem;
}

/* Title styling */
.popup h2 {
  font-size: 2rem;
  color: #333;
  margin: 0;
}

/* List styling */
.list {
  list-style-type: none;
  padding: 0;
  margin: 0 0 20px;
}

.list li {
  font-size: 1rem;
  color: #8f8f8f;
  margin: 10px 0;
  display: flex;
}

.list li::before {
  content: "▶";
  color: #666;
  margin-right: 8px;
}

/* Okay button styling */
.okayButton {
  margin-top: 1rem;
  background-color: #9258fe;
  font-size: 1.5rem;
  color: #ffff;
  border-radius: 1rem;
  padding: 0.5rem 6.5rem;
  text-decoration: none;
  border: none;
  box-shadow: 0px 10px 0px rgba(74, 45, 128, 1);
  cursor: pointer;
  width: 100%;
  font-family: var(--font-poppins);
}

/* Footer text styling */
.footer {
  font-size: 0.8rem;
  color: #888;
  margin-top: 10px;
}

.noScroll {
  overflow: hidden;
}
