"use client";
import Image from "next/image";
import styles from "./styles.module.css";

const ScreenRotationOverlay = ({ isOpen = false }) => {
  if (!isOpen) return null;

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <Image
          src="/images/rotate-prompt/rotating-top.webp"
          alt="Rotate device prompt"
          width={50}
          height={50}
          className={styles.rotatingIcon}
        />

        <h1 className={styles.title}>Turn your screen!</h1>

        <div className={styles.rotateImageContainer}>
          <Image
            src="/images/rotate-prompt/rotate.webp"
            alt="Rotate"
            width={200}
            height={200}
            className={styles.rotateMainImage}
          />
        </div>

        <Image
          src="/images/rotate-prompt/bottom-img.webp"
          alt="Bottom image"
          width={300}
          height={250}
          className={styles.bottomImage}
        />
      </div>
    </div>
  );
};

export default ScreenRotationOverlay;
