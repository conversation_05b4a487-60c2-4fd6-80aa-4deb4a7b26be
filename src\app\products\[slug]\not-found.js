import Link from "next/link";
import styles from "./not-found.module.css";

export default function NotFound() {
  return (
    <div className={styles.notFoundWrapper}>
      <div className={styles.notFoundContent}>
        <h1 className={styles.notFoundTitle}>Product Not Found</h1>
        <p className={styles.notFoundDescription}>
          Sorry, we couldn't find the product you're looking for.
        </p>
        <div className={styles.notFoundActions}>
          <Link href="/products" className={styles.backToProductsBtn}>
            ← Back to Products
          </Link>
          <Link href="/" className={styles.homeBtn}>
            Go to Home
          </Link>
        </div>
      </div>
    </div>
  );
}
