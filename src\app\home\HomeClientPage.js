"use client";
import useScrollAnimation from "@/hooks/useScrollAnimation";
import { gsap } from "gsap";
import { ScrollToPlugin } from "gsap/ScrollToPlugin";
import { ScrollTrigger } from "gsap/dist/ScrollTrigger";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Carousel from "../../components/CarouselHome";
import HomeThemeCarouselCt from "./HomeThemeCarouselCt";
import styles from "./styles.module.css";

gsap.registerPlugin(ScrollToPlugin);
gsap.registerPlugin(ScrollTrigger);

const HomeClientPage = () => {
  const t = useTranslations("HomePage");

  const setRef = useScrollAnimation(
    {
      duration: 1,
      ease: "power3.out",
      start: "top bottom",
    },
    []
  );

  return (
    <>
      <div className={styles.landingPageWrapper}>
        <Carousel />
        <div className={styles.nurturingGrowthWrapper}>
          <h2 className={styles.homeSubheaders} ref={(el) => setRef(el)}>
            {t("nurturingGrowth.title.part1")}{" "}
            <span className={styles.homeHighlightSubheader}>
              {t("nurturingGrowth.title.highlight")}{" "}
              <Image
                src="/images/homeArrow.png"
                width={60}
                height={60}
                className={styles.homeHighlightSubheaderImg}
                alt="Arrow"
              />
            </span>{" "}
            {t("nurturingGrowth.title.part2")}
            <br />
            {t("nurturingGrowth.title.part3")}
          </h2>
          <div className={styles.nurturingGrowthCardsWrapper}>
            <div className={styles.nurturingGrowthCards}>
              <Image
                src="/images/homeNurturing1.png"
                width={320}
                height={291}
                alt={t("nurturingGrowth.cards.card1")}
              />
              <p className={styles.nurturingGrowthCardCont}>{t("nurturingGrowth.cards.card1")}</p>
            </div>
            <div className={styles.nurturingGrowthCards}>
              <Image
                src="/images/homeNurturing2.png"
                alt={t("nurturingGrowth.cards.card2")}
                width={320}
                height={291}
              />
              <p className={styles.nurturingGrowthCardCont}>{t("nurturingGrowth.cards.card2")}</p>
            </div>
            <div className={styles.nurturingGrowthCards}>
              <Image
                src="/images/homeNurturing3.png"
                width={320}
                height={291}
                alt={t("nurturingGrowth.cards.card3")}
              />
              <p className={styles.nurturingGrowthCardCont}>{t("nurturingGrowth.cards.card3")}</p>
            </div>
          </div>
        </div>
        <div ref={(el) => setRef(el)}>
          <h2 className={styles.homeSubheaders}>
            {t("discoverThemes.title.part1")} <br />
            <span className={styles.homeHighlightSubheader}>
              {t("discoverThemes.title.highlight1")}{" "}
              <Image
                src="/images/homeHat.png"
                alt="Hat"
                width={60}
                height={60}
                className={styles.homeHighlightSubheaderImg}
              />
            </span>{" "}
            {t("discoverThemes.title.and")}{" "}
            <span className={styles.homeHighlightSubheader}>
              {t("discoverThemes.title.highlight2")}{" "}
              <Image
                src="/images/homeRemote.png"
                alt="Remote"
                width={60}
                height={60}
                className={styles.homeHighlightSubheaderImg}
              />
            </span>{" "}
          </h2>
          <HomeThemeCarouselCt />
        </div>
      </div>
    </>
  );
};

export default HomeClientPage;
