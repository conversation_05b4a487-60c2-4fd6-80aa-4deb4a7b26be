"use client";
import { useEffect } from "react";
import Image from "next/image";
import styles from "./styles.module.css";
import Link from "next/link";

const ModalFullScreen = ({ toggleModal, modalData, isOpen }) => {
  useEffect(() => {
    if (isOpen) {
      document.body.classList.add(styles.noScroll);
    } else {
      document.body.classList.remove(styles.noScroll);
    }
    return () => {
      document.body.classList.remove(styles.noScroll);
    };
  }, [isOpen]);
  return (
    <>
      <button className={styles.openButton} onClick={toggleModal}>
        Open Modal
      </button>
      <div className={styles.modalOverlay} onClick={toggleModal}>
        <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
          <button className={styles.closeButton} onClick={toggleModal}>
            X
          </button>
          <div className={styles.modalImageWrapper}>
            {modalData.gamePopinImages.map((img, index) => (
              <div className={styles.popupImgBox} key={index}>
                <Image
                  src={img}
                  width={184}
                  height={328}
                  alt={`Image ${index + 1}`}
                  placeholder="blur"
                  blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNctmhZPQAGUwJv7ObBEQAAAABJRU5ErkJggg=="
                />
              </div>
            ))}
          </div>
          <div>
            <h2>{modalData.gameName}</h2>
            <p className={styles.gameDescription}>{modalData.gameDesc}</p>
            <p>{modalData.gameContentInside.description1}</p>
            <p>{modalData.gameContentInside.description2}</p>
            <div className={styles.modalBtnWrappers}>
              {modalData.storeLinks?.appStore && (
                <Link
                  className={styles.storesBtn}
                  href={modalData.storeLinks.appStore}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  App store
                </Link>
              )}
              {modalData.storeLinks?.playStore && (
                <Link
                  className={styles.storesBtn}
                  href={modalData.storeLinks.playStore}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Play store
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
export default ModalFullScreen;
