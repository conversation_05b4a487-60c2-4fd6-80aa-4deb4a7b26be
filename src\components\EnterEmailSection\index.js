"use client";
import CarouselEnterEmail from "@/components/CarouselEnterEmail/page";
import FormCtaButton from "@/components/FormCtaButton";
import InfoPopup from "@/components/WebGlInfoPopup";
import { inforPopupData } from "@/constants";
import { usePlayerContext } from "@/context/CreatePlayerContext";
import useStripeCheckout from "@/hooks/useStripeCheckout";
import apiClient from "@/utils/axiosUtil";
import { getLocale } from "@/utils/helperFunctions";
import { trackWebEngageEvent, webEngagelogin } from "@/utils/webengage";
import { useLocale, useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import styles from "./styles.module.css";

const EnterEmailSection = () => {
  const { playerData, updatePlayerData } = usePlayerContext();
  const [showPopup, setShowPopup] = useState(false);
  const [email, setEmail] = useState(playerData.userEmail || "");
  const [isChecked, setIsChecked] = useState(false);
  const [errors, setErrors] = useState({ email: "", checkbox: "", server: "" });
  const [isLoading, setLoading] = useState(false);
  const locale = useLocale();
  const lang = getLocale(locale);
  const router = useRouter();
  const { handleCheckout } = useStripeCheckout();
  const planID = localStorage.getItem("selectedPlan");
  const t = useTranslations("EnterEmailSection");

  const closePopup = () => {
    setShowPopup((prev) => !prev);
  };

  const handleEmailChange = (e) => {
    setEmail(e.target.value);
    setErrors((prevErrors) => ({ ...prevErrors, email: "", server: "" }));
    updatePlayerData({ userEmail: e.target.value });
  };

  const handleCheckboxChange = (e) => {
    setIsChecked(e.target.checked);
    setErrors((prevErrors) => ({ ...prevErrors, checkbox: "" }));
  };

  const validateEmail = (email) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
  };

  const verifyEmailWithKickbox = async (email) => {
    try {
      const response = await apiClient.get(
        `${window.location.origin}/api/verify-email?email=${email}`
      );
      return response.data.result !== "undeliverable";
    } catch (error) {
      // Handle Kickbox verification error
      return false;
    }
  };

  const getGradeCall = async () => {
    const url = `${process.env.NEXT_PUBLIC_PRODUCTSERVICE_BASE_URL}/grade?version=6.0&l=${lang}`;
    try {
      const response = await apiClient.get(url);

      return response.data.filter(
        (item) => parseInt(item.AgeText) === parseInt(playerData.selectedAge)
      );
    } catch (error) {
      setLoading(false);
    }
  };

  const createPlayerApiCall = async (authToken, grade) => {
    const url = `${process.env.NEXT_PUBLIC_USERSERVICE_BASE_URL}/player?version=8.0&gameid=doctor&l=${lang}&platform=web`;
    const data = {
      nickname: playerData.name,
      gender: playerData.selectedAvatar.gender,
      grade: grade,
      subject: "maths",
      avatar_name: playerData.selectedAvatar.name,
      avatar_index: playerData.selectedAvatar.index,
      level: 1,
      os_version: "iOS 13.1.2",
      language: lang,
      game: "sdk",
      device_id: "2102279051E429E7F61AA1E6E4DF928219216B64E6C7067498B8DF5BC7CC4RROR",
      device_model: "iPad6,11",
      time_zone: "6",
      sdk_version: "4.0",
    };

    try {
      const response = await apiClient.post(url, data);
      await updatePlayerInterests(response.data.Id, authToken);
    } catch (error) {
      // Handle error creating player
      setLoading(false);
    }
  };

  const hitRegisterAPI = async () => {
    trackWebEngageEvent("WebEmailScrNextBtnClk");
    setLoading(true);
    setErrors({ email: "", checkbox: "", server: "" });

    // Validation
    const validationErrors = {};
    if (!validateEmail(email)) validationErrors.email = t("EmailError");
    if (!isChecked) validationErrors.checkbox = t("ErrorCheckbox");

    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      setLoading(false);
      return;
    }

    // Kickbox verification
    const isEmailValid = await verifyEmailWithKickbox(email);
    if (!isEmailValid) {
      trackWebEngageEvent("WebEmailValidationFailedRc");
      setErrors((prevErrors) => ({
        ...prevErrors,
        email: t("InvalidEmailKickbox"),
      }));
      setLoading(false);
      return;
    }

    trackWebEngageEvent("WebEmailValidationSuccessRc");
    const url = `${process.env.NEXT_PUBLIC_USERSERVICE_BASE_URL}/register?language=${lang}&gameid=doctor&version=8.0&platform=web`;
    const data = {
      email,
      language: lang,
      locale: lang.toUpperCase(),
      game: "doctor",
      sdk_version: "8.1",
      terms_confirmed: 1,
      onboarding_collection_id: "default",
    };

    try {
      const response = await apiClient.post(url, data);
      if (response) {
        webEngagelogin({ email, userId: response.data.Id });
        const authToken = localStorage.getItem("auth_token");
        const gradeVal = await getGradeCall();
        await createPlayerApiCall(authToken, gradeVal[0].Grade);
      }
    } catch (error) {
      trackWebEngageEvent("WebEmailValidationSuccessAfterErrorRc");
      console.error({ error });

      setErrors((prevErrors) => ({
        ...prevErrors,
        server: (
          <>
            {error?.response?.data?.Message}{" "}
            <Link href="/login">
              <span style={{ color: "blue", cursor: "pointer" }}>{t("LoginCta")}</span>
            </Link>
          </>
        ),
      }));
      setLoading(false);
    }
  };

  const updatePlayerInterests = async (playerId, authToken) => {
    const url = `${process.env.NEXT_PUBLIC_USERSERVICE_BASE_URL}/player/interest?version=8.1&l=${lang}`;
    const data = {
      player_id: playerId,
      game: "sdk",
      platform: "ios",
      sdk_version: "8.0",
      interests: playerData.selectedThemes,
    };

    try {
      await apiClient.put(url, data);
      if (planID) {
        handleCheckout({ lang, planID, authToken });
      } else {
        router.push("/purchase");
      }
      localStorage.setItem("playerId", playerId);
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  return (
    <div className={styles.bodyWrapper}>
      <div className={styles.getStartedWrapper}>
        <CarouselEnterEmail />
        <div className={styles.enterEmailWrapper}>
          <div className={styles.inputWrapper}>
            <input
              id="name"
              type="text"
              placeholder={t("EmailPlaceholder")}
              value={email}
              className={`${errors.email ? styles.errorEmailInput : ""}`}
              onChange={handleEmailChange}
            />
          </div>
          <div
            className={`${styles.inputCheckboxTextWrapper} ${
              errors.checkbox ? styles.errorcheckBox : ""
            }`}
            role="checkbox"
            aria-checked={isChecked}
            tabIndex={0}
            onClick={handleCheckboxChange}
            onKeyPress={(e) => {
              if (e.key === "Enter" || e.key === " ") {
                handleCheckboxChange(e);
              }
            }}
          >
            <input
              type="checkbox"
              id="customCheckbox"
              checked={isChecked}
              onChange={handleCheckboxChange}
              disabled={!email}
            />
            <label
              htmlFor="customCheckbox"
              className={styles.checkboxLabel}
              aria-label={t("Checkbox")}
            >
              <div style={{ marginTop: "-4px" }}>
                <p style={{ color: "#000", margin: 0 }}>{t("Checkbox")}</p>
                <a
                  href="/privacy-policy"
                  className={styles.privacyLink}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {t("CheckboxDataPrivacy")}
                </a>
              </div>
            </label>
          </div>
          {(errors.email || errors.checkbox || errors.server) && (
            <div className={styles.infoWrapper}>
              <p>
                <Image src="/images/webGl/warning.png" height={15} width={15} alt="Warning" />{" "}
                {errors.email || errors.checkbox || errors.server}
              </p>
            </div>
          )}
          <FormCtaButton text={t("CtaBtn")} onClick={hitRegisterAPI} loading={isLoading} />
          <p className={styles.infoText} onClick={() => setShowPopup((prev) => !prev)}>
            <Image src="/images/webGl/normalInfo.png" height={15} width={15} alt="Info" />{" "}
            {t("InfoText")}
          </p>
        </div>
      </div>
      {showPopup && (
        <InfoPopup
          isOpen={showPopup}
          onClose={closePopup}
          data={inforPopupData[2]}
          localeData="EnterEmailSection"
        />
      )}
    </div>
  );
};

export default EnterEmailSection;
