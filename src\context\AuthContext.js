// contexts/AuthContext.js
"use client";
import { webEngageLogout } from "@/utils/webengage";
import { createContext, useContext, useEffect, useState } from "react";

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);

  useEffect(() => {
    const userDetails = JSON.parse(localStorage.getItem("UserDetails"));
    // Get Cookie value
    const token = document.cookie
      .split("; ")
      .find((row) => row.startsWith("token="))
      ?.split("=")[1];

    if (userDetails && token) {
      setIsLoggedIn(true);
      setIsSubscribed(userDetails.isSubscribed);
    }
  }, []);

  const login = (subscriptionStatus) => {
    setIsLoggedIn(true);
    setIsSubscribed(subscriptionStatus);
  };

  const logout = () => {
    webEngageLogout();
    setTimeout(() => {
      document.cookie = "token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
      setIsLoggedIn(false);
      setIsSubscribed(false);
      localStorage.clear();
    }, 500);
  };

  return (
    <AuthContext.Provider value={{ isLoggedIn, isSubscribed, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
