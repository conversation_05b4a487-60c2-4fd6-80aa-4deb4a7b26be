import { useTranslations } from "next-intl";
import BlogDetails from "./BlogDetails";

export async function generateMetadata({ params }) {
  const { blogID } = params;

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL}/wp-json/wp/v2/posts?slug=${blogID}`,
    {
      next: { revalidate: 3600 }, // Revalidate every hour
    }
  );

  if (!response.ok) {
    return {
      title: "Post Not Found | SKIDOS Blog",
      description: "The requested blog post could not be found.",
    };
  }

  const postData = await response.json();
  const post = postData[0];
  const yoast = post?.yoast_head_json;

  return {
    title: yoast?.title || post?.title?.rendered,
    description: yoast?.description || post?.excerpt?.rendered?.slice(0, 160),
    openGraph: {
      title: yoast?.og_title,
      description: yoast?.og_description,
      type: "article",
      publishedTime: post?.date,
      modifiedTime: post?.modified,
      images: [
        {
          url: yoast?.og_image?.[0]?.url,
          width: yoast?.og_image?.[0]?.width,
          height: yoast?.og_image?.[0]?.height,
          alt: yoast?.og_image?.[0]?.alt || post?.title?.rendered,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: yoast?.twitter_title,
      description: yoast?.twitter_description,
      images: [yoast?.twitter_image],
    },
    alternates: {
      canonical: `https://skidos.com/blogs/${blogID}`,
    },
  };
}

const NotFound = () => {
  const t = useTranslations("News");
  return (
    <div>
      <h1>{t("postNotFound")}</h1>
    </div>
  );
};

const BlogStructuredData = ({ post }) => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    headline: post?.title?.rendered,
    description: post?.excerpt?.rendered,
    image: post?.yoast_head_json?.og_image?.[0]?.url,
    datePublished: post?.date,
    dateModified: post?.modified,
    author: {
      "@type": "Organization",
      name: "SKIDOS",
      url: "https://skidos.com",
    },
    publisher: {
      "@type": "Organization",
      name: "SKIDOS",
      logo: {
        "@type": "ImageObject",
        url: "https://skidos.com/logo.png", // Update with your actual logo URL
      },
    },
    mainEntityOfPage: {
      "@type": "WebPage",
      "@id": `https://skidos.com/blogs/${post?.slug}`,
    },
  };

  return <script type="application/ld+json">{JSON.stringify(structuredData)}</script>;
};

const BlogPage = async ({ params }) => {
  const { blogID } = params;

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL}/wp-json/wp/v2/posts?slug=${blogID}`,
    {
      cache: "no-store",
    }
  );

  if (!response.ok) {
    return <NotFound />;
  }

  const postData = await response.json();
  const post = postData[0];

  return (
    <>
      <BlogStructuredData post={post} />
      <BlogDetails params={params} />
    </>
  );
};

export default BlogPage;
