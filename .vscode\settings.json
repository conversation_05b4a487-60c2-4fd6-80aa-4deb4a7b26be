{"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.tabSize": 2, "files.eol": "\n", "prettier.requireConfig": true, "prettier.configPath": ".prettier<PERSON>", "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "javascript.updateImportsOnFileMove.enabled": "always", "[javascript]": {"editor.defaultFormatter": "rvest.vs-code-prettier-eslint"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "rvest.vs-code-prettier-eslint"}, "[css]": {"editor.defaultFormatter": "rvest.vs-code-prettier-eslint"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}