"use client";
import useIsMobile from "@/hooks/useIsMobile";
import useScrollAnimation from "@/hooks/useScrollAnimation";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useEffect } from "react";
import Mascots from "../../../public/images/contactUs/contactUsMascots.webp";
import styles from "./styles.module.css";

const PartnershipClient = () => {
  const t = useTranslations("Partnership");
  const isMobile = useIsMobile();
  const setRef = useScrollAnimation(
    {
      duration: 1,
      ease: "power3.out",
      start: "top bottom",
    },
    []
  );
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  return (
    <>
      <div className={styles.partnershipWrapper}>
        <header className={styles.contactUsHeader}>
          <div ref={(el) => setRef(el)}>
            <h2>{t("title")}</h2>
            {isMobile ? (
              <p>{t("subtitle.mobile")}</p>
            ) : (
              <p>
                {t("subtitle.desktop1")}
                <br />
                {t("subtitle.desktop2")}
              </p>
            )}
            <button className={styles.carouselBannerBtn}>
              <a
                className="noLinkStyle"
                href="mailto:<EMAIL>"
                target="_blank"
                rel="noopener noreferrer"
                data-stringify-link="mailto:<EMAIL>"
                data-sk="tooltip_parent"
                aria-haspopup="menu"
                aria-expanded="false"
              >
                {t("collaborate")}
              </a>
            </button>
          </div>
          <div ref={(el) => setRef(el)}>
            <Image alt={t("mascotsAlt")} src={Mascots} className={styles.contactUsMascotsImg} />
          </div>
        </header>
        <section className={styles.ourPartnersWrapper} ref={(el) => setRef(el)}>
          <h1>{t("ourPartners")}</h1>
          <div>
            <Image
              alt={t("mitgameAlt")}
              src="/images/partnership/mitgame.png"
              width={isMobile ? 250 : 413}
              height={isMobile ? 120 : 200}
            />
            <Image
              alt={t("prodegeAlt")}
              src="/images/partnership/prodege.png"
              width={isMobile ? 200 : 360}
              height={isMobile ? 70 : 100}
            />
          </div>
        </section>
        <section className={styles.waysCollaborateWrapper} ref={(el) => setRef(el)}>
          <h1>{t("waysCollaborate")}</h1>
          <div className={styles.typeOfWays}>
            <div>
              <div className={styles.blueBg}>
                <Image
                  src="/images/partnership/handShake.png"
                  height={147}
                  width={147}
                  alt={t("brandPartnershipAlt")}
                />
              </div>
              <p>{t("brandPartnership")}</p>
            </div>
            <div>
              <div className={styles.blueBg}>
                <Image
                  src="/images/partnership/certificate.png"
                  height={147}
                  width={147}
                  alt={t("contentLicensingAlt")}
                />
              </div>
              <p>{t("contentLicensing")}</p>
            </div>
            <div>
              <div className={styles.blueBg}>
                <Image
                  src="/images/partnership/handHeart.png"
                  height={147}
                  width={147}
                  alt={t("csrAlt")}
                />
              </div>
              <p>{t("csr")}</p>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default PartnershipClient;
