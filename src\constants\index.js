import MathImg from "../../public/images/math.webp";
import DoctorImg from "../../public/images/product/games/doctor.webp";

// import {useTranslations} from 'next-intl';
// const t = useTranslations('Acquisition');

export const learningScreenshots = [
  "/images/productModal/LearningScreenshots/2.webp",
  "/images/productModal/LearningScreenshots/3.webp",
  "/images/productModal/LearningScreenshots/4.webp",
  "/images/productModal/LearningScreenshots/6.webp",
  "/images/productModal/LearningScreenshots/1.webp",
  "/images/productModal/LearningScreenshots/5.webp",
];

export const gamesData = [
  {
    gameName: "Doctor Games for Kids",
    gameDesc: "Explore doctor game for kids - health, hygiene, & check-ups",
    gameSkill: "Empathy & Compassion",
    interests: ["PRETEND PLAY", "CASU<PERSON>", "PUZ<PERSON><PERSON>"],
    thumbnailImage: DoctorImg,
    heroSection: {
      title: "Doctor Games for Kids",
      subtitle: "Explore doctor game for kids - health, hygiene, & check-ups",
      downloadButtons: [
        {
          type: "appStore",
          url: "https://apps.apple.com/us/app/doctor-games-for-kids/id1506886061",
        },
        {
          type: "playStore",
          url: "https://play.google.com/store/apps/details?id=skidos.preschool.kids.doctor.game",
        },
        {
          type: "amazonStore",
          url: "https://www.amazon.com/gp/product/B0F2HQ8DFF",
        },
        {
          type: "webStore",
          url: "https://skidos.com/get-started/",
        },
      ],
      ratings: {
        stars: 4.3,
        totalRatings: "36.3K",
      },
      webBanner: "ImageURL",
      mobileBanner: "ImageURL",
    },
    mainContent: {
      videoUrl: "https://www.youtube.com/watch?v=IkL0O5pTPls",
      aboutSection: {
        title: "About SKIDOS Doctor",
        features: [
          "Fun doctor game for kids with adorable little patients",
          "Treat flu, earaches & tooth aches through interactive play",
          "Build empathy & compassion with role-playing scenarios",
          "Boost cognitive skills with engaging medical challenges",
        ],
        description:
          "SKIDOS Doctor Game lets kids explore a cheerful doctor clinic with 3 adorable patients, each needing care for common ailments like flu, earaches, toothaches, and minor injuries. As one of the most engaging doctor games for kids online, this doctor simulation game allows children to play through fun scenes - cleaning wounds, taking X-rays, brushing teeth, and using real-life tools like thermometers and ear drops. It’s a delightful mix of pretend play and health awareness, wrapped in colorful animations and kid-friendly storytelling, making it a standout among medical games online. Parents and children love this fun doctor role play app for its simplicity, educational value, and imaginative fun!",
      },
    },
    learningSection: {
      title: "What Kids Learn?",
      description:
        "SKIDOS Doctor Game helps kids build cognitive thinking, empathy, creativity, and critical thinking through fun medical role-play. This hospital simulation game isn't just entertaining - kids learn to diagnose, treat, and care for patients while developing essential child development skills and also, enhancing fine motor skills. Educational activities are seamlessly woven into gameplay to support holistic development, making it one of the most thoughtfully designed doctor games for kids online.",
      skills: [
        { title: "Empathy & Compassion", thumbnailImg: "imgURL" },
        { title: "High-Impact Cognitive Skills", thumbnailImg: "imgURL" },
        { title: "Health Awareness & Hygiene", thumbnailImg: "imgURL" },
      ],
    },
    ageSection: {
      title: "Perfect for Kids Aged 3-8",
      description:
        "This kids doctor app is ideal for kids aged 3 to 8 who love learning through pretend play and want to explore the world of healthcare in a safe learning environment. It’s especially great for preschoolers and early learners developing essential life skills through interactive role-play. As one of the most engaging doctor games for kids online, it offers a playful yet educational experience that nurtures curiosity and empathy.",
    },
    screenshots: [
      "/images/productModal/Doctor/doctor1.webp",
      "/images/productModal/Doctor/doctor2.webp",
      "/images/productModal/Doctor/doctor3.webp",
      "/images/productModal/Doctor/doctor4.webp",
    ],
    testimonials: [
      {
        author: "Rihanna’s Mom",
        ageDescription: "Mother of a 6-year-old",
        rating: "5",
        title: "A Perfect Mix of Fun & Learning",
        content:
          "A perfect mix of fun & learning! My child loves being the doctor in this game, it’s playful, interactive, and educational at the same time. Great way to teach empathy, problem-solving, and basic medical concepts. Highly recommend for curious little minds!",
      },
      {
        author: "Fredrikdad",
        ageDescription: "Father of a 5-year-old",
        rating: "4",
        title: "A Fun & Educational Doctor Game for Kids",
        content:
          "Good doctor game for kids. This one is a hit in my house. My daughter pretends to be a doctor but she is also learning about emotions, hygiene and responsibility. Its good and looks like storytelling with purpose.",
      },
      {
        author: "Taylor’s Mama",
        ageDescription: "Mother of a 2.5-year-old",
        rating: "5",
        content:
          "My toddler loves this game. I got this game for my daughter when she was 1½, and she absolutely loved it! She’s 2½ now and loves it even more—especially the doctor game. Making the characters feel better brings her so much joy, and that makes me so happy too!",
      },
    ],
    vsps: [
      {
        title: "Safe Learning Environment",
        thumbnailImg: "imageUrl",
      },
      {
        title: "Role-Playing with Educational Impact",
        thumbnailImg: "imageUrl",
      },
      {
        title: "Builds Critical Life Skills Through Play",
        thumbnailImg: "imageUrl",
      },
    ],
    faqs: [
      {
        question: "How can I download the game?",
        answer:
          "You can download SKIDOS Doctor Game from the App Store (iOS) or Google Play Store (Android). You can also search for “SKIDOS Doctor Games for Kids” and tap the install button. Install this best doctor game for kids today and let your child become a tiny healer in their very own hospital!",
      },
      {
        question: "Is the game free to play?",
        answer:
          "The game is free to play and download after creating a SKIDOS account for a limited time every day. You can activate a free trial for 3 days to experience the complete product. To unlock all features and educational content for more than 3 days, a SKIDOS subscription is required.",
      },
      {
        question: "What devices is the game compatible with?",
        answer:
          "SKIDOS Doctor Game is compatible with most smartphones, tablets running iOS or Android and can also be played on Amazon Fire tablet and web. Make sure your device has the latest OS version for optimal performance.",
      },
      {
        question: "How do I contact support if I face issues?",
        answer:
          "You can reach SKIDOS support through the app or by visiting the official website at www.skidos.com. There’s a dedicated help section for troubleshooting and inquiries.",
      },
    ],
    discoverMOreFunGames: [
      {
        gameName: "Bath",
        gameDesc: "Help kids enjoy daily bathroom routines with cute friends!",
        gameSkill: "Health & Hygiene",
      },
      {
        gameName: "Shopping Mall",
        gameDesc: "Fun pretend play with shopping & decision making!",
        gameSkill: "Life Skills & Imagination",
      },
      {
        gameName: "Motorcycle Games",
        gameDesc: "Ride, race & learn with math games on bikes!",
        gameSkill: "Motor Skills & Logic",
      },
    ],
  },
  {
    gameName: "Math Learning",
    gameDesc: "Master Math Skills - engage with an interactive math curriculum",
    gameSkill: "Cognitive Development",
    interests: ["EDUCATIONAL", "MATH", "PUZZLE"],
    thumbnailImage: MathImg, // Replace with actual import
    heroSection: {
      title: "Math Learning",
      subtitle: "Master Math Skills - engage with an interactive math curriculum",
      downloadButtons: [
        {
          type: "appStore",
          url: "https://apps.apple.com/us/app/skidos-math/idXXXXX", // Replace with actual App Store URL
        },
        {
          type: "playStore",
          url: "https://play.google.com/store/apps/details?id=skidos.math.learning.game", // Replace with actual Play Store URL
        },
        {
          type: "amazonStore",
          url: "https://www.amazon.com/gp/product/B0XXXXX", // Replace with actual Amazon URL
        },
        {
          type: "webStore",
          url: "https://skidos.com/get-started/",
        },
      ],
      thumbnailImage: MathImg,
      ratings: {
        stars: 4.3,
        totalRatings: "36.3K",
      },
    },
    mainContent: {
      videoUrl: "https://www.youtube.com/watch?v=XXXXX", // Replace with actual YouTube video ID
      aboutSection: {
        title: "About SKIDOS Math",
        features: [
          "Boost numeracy skills with math games for kids",
          "Math curriculum aligned with US & UK standards",
          "Mascots guide, reward & boost math learning",
          "Classroom-tested math learning app for kids",
        ],
        description:
          "Discover SKIDOS Math, a classroom-tested math learning app for kids aged 3–10! Our fun math game app, aligned with the US Common Core and UK National Curriculum, outperforms traditional teaching in accuracy, engagement, interest, and curiosity. Kids love SKIDOS digital math games over physical worksheets. Playful mascots guide the learning journey with rewards and feedback, ensuring well-graded progress across math topics. The intuitive, kid-friendly interface features vibrant graphics and diverse avatars for inclusivity. Enjoy online math games for kids, from preschool to grade 5, and ignite a passion for math learning!",
      },
    },
    learningSection: {
      title: "What Kids Learn?",
      description:
        "Children build essential skills with SKIDOS through fun and engaging math games for kids. Cognitive development grows as interactive puzzles boost problem-solving, logical thinking, and accuracy. Narrated questions like “Which shape is a circle?” introduce math vocabulary and strengthen listening comprehension. Executive function improves as kids plan and focus on sequenced tasks. This math education app promotes numeracy fluency with practice in numbers, operations, and shapes. Critical thinking develops as children analyze challenges, while rewarding feedback builds math confidence. With playful visuals, mascots, and curriculum-based questions, SKIDOS makes learning fun - teaching counting, shapes, addition, and more.",
      skills: [
        { title: "Cognitive Development", color: "#FFE5E5" },
        { title: "Numeracy Fluency", thumbnailImg: "imgURL" },
        { title: "Executive Function", color: "#E5FFE5" },
      ],
    },
    ageSection: {
      title: "Perfect for Kids Aged 3-10",
      description:
        "SKIDOS Math is designed for children aged 3-10, from preschoolers to early elementary learners. It offers not just math learning games for preschoolers but also engaging math learning activities for older children in early primary grades. Aligned with the US Common Core and UK National Curriculum, this math learning app for kids effectively supports young learners at every stage.",
    },
    screenshots: [
      "/images/productModal/Math/math1.webp",
      "/images/productModal/Math/math2.webp",
      "/images/productModal/Math/math3.webp",
      "/images/productModal/Math/math4.webp",
    ],
    testimonials: [
      {
        author: "Harper's Dad",
        ageDescription: "Father of a 7-year-old",
        rating: "5",
        content:
          "Finally, a math app my kid asks to play! SKIDOS Math has completely changed how my daughter feels about math and numbers. She used to get anxious with worksheets, but now she’s solving problems while playing her favorite games. It’s fun, smart, and actually works!",
      },
      {
        author: "MapleMomUK",
        ageDescription: "Mother of a 6-year-old",
        rating: "4",
        content:
          "A great way to learn! We’ve tried a few math apps, but SKIDOS stands out. The activities are well-designed and grow with my child’s skills. It’s not just repetition - it’s real learning wrapped in fun. My little one confidently named 2D & 3D shapes during a class activity, and the teacher mentioned how much progress they’ve made. I could see how the practice in SKIDOS really paid off.",
      },
      {
        author: "EllaBearMama",
        ageDescription: "Mother of a 5-year-old",
        rating: "5",
        content:
          "This is the only app I don’t feel guilty about! I love how SKIDOS blends math into everyday play. My son doesn’t even realize he’s learning - he just thinks he’s collecting gems and coins! The content is age-appropriate, and I can observe his progress in school due to this. Total win for us.",
      },
    ],
    vsps: [
      {
        title: "Play-Based Learning That Actually Teaches",
        thumbnailImg: "vspMath1Img",
      },
      {
        title: "Supports Early Learning",
        thumbnailImg: "vspMath2Img",
      },
      {
        title: "Aligned With Global Educational Standards",
        thumbnailImg: "vspMath3Img",
      },
    ],
    faqs: [
      {
        question: "How can I download the SKIDOS math games for kids?",
        answer:
          "You can download SKIDOS math games from the App Store (iOS) or Google Play Store (Android). Install this fun math game app, search for SKIDOS Math and help your child strengthen numeracy skills with fun, educational games.",
      },
      {
        question: "Is the game free to play?",
        answer:
          "The game is free to play and download after creating a SKIDOS account for a limited time every day. You can activate a free trial for 3 days to experience the complete product. To unlock all features and educational content for more than 3 days, a SKIDOS subscription is required.",
      },
      {
        question: "What devices is the game compatible with?",
        answer:
          "SKIDOS math learning app for kids is compatible with most smartphones, tablets running iOS or Android and can also be played on Amazon Fire tablet and web. Make sure your device has the latest OS version for optimal performance.",
      },
      {
        question: "How many kids can use one SKIDOS account?",
        answer:
          "With a SKIDOS subscription, you can create up to 6 child profiles on one account - perfect for families with multiple kids.",
      },
    ],
    discoverMOreFunGames: [
      {
        gameName: "NumCraft",
        gameDesc: "Explore numbers & patterns through interactive play!",
        gameSkill: "Logical Reasoning",
        thumbnailImage: DoctorImg, // Replace with actual image variable
      },
      {
        gameName: "Magic Math",
        gameDesc: "Learn math with magical puzzles and spells!",
        gameSkill: "Math Confidence",
        thumbnailImage: MathImg, // Replace with actual image variable
      },
      {
        gameName: "Doctor Games for Kids",
        gameDesc: "Explore doctor game for kids - health, hygiene, & check-ups",
        gameSkill: "Empathy & Compassion",
        thumbnailImage: DoctorImg, // Already used in previous object
      },
    ],
  },
];

export const themeFilters = [
  "All",
  "Action",
  "Casual",
  "Creative",
  "Pretend Play",
  "Racing",
  "Music",
  "Puzzle",
  "Sports",
  "Tracing",
  "Learn To Read",
  "Math",
  "Fitness",
  "Emotional Wellbeing",
];

export const themeCardsRow1 = [
  {
    themeName: "Action",
    themeDesc: "Thrilling action games to boost quick thinking",
    themeImg: "/images/action.webp",
  },
  {
    themeName: "Casual",
    themeDesc: "Relaxed casual games for fun and learning",
    themeImg: "/images/casual.webp",
  },
  {
    themeName: "Creative",
    themeDesc: "Innovative games to unleash your child's creativity",
    themeImg: "/images/creative.webp",
  },
  {
    themeName: "Music",
    themeDesc: "Melodious music games to nurture musical talent",
    themeImg: "/images/music.webp",
  },
  {
    themeName: "Puzzle",
    themeDesc: "Challenging puzzles to enhance problem-solving abilities",
    themeImg: "/images/puzzle.webp",
  },
  {
    themeName: "Pretend Play",
    themeDesc: "Imaginative play games to spark creativity",
    themeImg: "/images/rolePlay.webp",
  },
  {
    themeName: "Racing",
    themeDesc: "Exciting racing games to develop strategic skills",
    themeImg: "/images/racing.webp",
  },
];

export const themeCardsRow2 = [
  {
    themeName: "Sports",
    themeDesc: "Interactive sports games to encourage active learning",
    themeImg: "/images/sports.webp",
  },
  {
    themeName: "Math",
    themeDesc: "Engaging educational games to build strong numeracy skills",
    themeImg: "/images/math.webp",
  },
  {
    themeName: "Learn To Read",
    themeDesc: "Interactive and engaging activities to foster early reading",
    themeImg: "/images/learntoread.webp",
  },
  {
    themeName: "Tracing",
    themeDesc: "Fun tracing activities for writing practice",
    themeImg: "/images/tracing.webp",
  },
  {
    themeName: "Emotional Wellbeing",
    themeDesc: "Games to support emotional health and resilience",
    themeImg: "/images/emotional.webp",
  },
  {
    themeName: "Fitness",
    themeDesc: "Active games to promote healthy habits and fun exercises",
    themeImg: "/images/fitness.webp",
  },
];

export const aboutValues = [
  {
    title: "Collaboration",
    description: "Work together to achieve goals and build positive work relationships",
  },
  {
    title: "Customer Centricity",
    description: "Listen to all that customers say, act on what makes sense to the business",
  },
  {
    title: "Curiosity & Creativity",
    description: "The Skidos approach ensure learning is fun and engaging for kids",
  },
  {
    title: "Confident Learners",
    description: "We facilitate experiences that propel your kid’s confidence in learning",
  },
  {
    title: "Active Screen Time",
    description: "In a way that keeps them active and generate healthy habits",
  },
  {
    title: "Innovation",
    description: "Innovate to solve the biggest organizational challenges.",
  },
];

export const HomeStreet = [
  {
    starImg: "/images/4.5_star.png",
    content: "Testimionial1",
    description: "Testimionial1Parent",
  },
  {
    starImg: "/images/4.5_star.png",
    content: "Testimionial2",
    description: "Testimionial2Parent",
  },
  {
    starImg: "/images/4_star.png",
    content: "Testimionial3",
    description: "Testimionial3Parent",
  },
  {
    starImg: "/images/4.5_star.png",
    content: "Testimionial4",
    description: "Testimionial4Parent",
  },
  {
    starImg: "/images/4.5_star.png",
    content: "Testimionial5",
    description: "Testimionial5Parent",
  },
];

export const LandingReview = [
  {
    starImg: "/images/4.5_star.png",
    content:
      "It's the best doctor game for little ones and it's super good at making learning fun. My 3 year old is getting better at their ABCs and maths while pretending to be a dentist. They're also learning how to take care of their teeth and brush properly. Great job, developers!",
    description: "Lucia, Mother of 3 year old",
  },
  {
    starImg: "/images/4.5_star.png",
    content:
      "A fantastic and engaging game for children. They include coding lessons, which is excellent. My 7 year old loves this, and especially enjoyed being able to select puzzles and challenges within her most beloved topics of interest. Highly recommended!",
    description: "Jody, Father of 7 year old",
  },
  {
    starImg: "/images/4_star.png",
    content:
      "Kids in my school loves to play this game a lot. Further more it enhanced their math. Cool. Need more game like this",
    description: "Ravinder Saxena, Father of 5 year old",
  },
  {
    starImg: "/images/4.5_star.png",
    content:
      "My 5 years old loved this pet care game. This virtual game for toddlers is a wonderful preschool learning game. She had so much fun caring for the pets along with learning to trace letters and numbers! 5 stars!",
    description: "Milikatar, Father of 5 year old",
  },
  {
    starImg: "/images/4.5_star.png",
    content:
      "I babysat my 4- and 6-year-old niece and nephew, and they loved this learning game. I liked that they learned independently, and it was safe for them. I found they offer more such games, so babysitting will be fun!",
    description: "CattMae, Mother of 4 and 6",
  },
];

export const ArticleMedia = [
  {
    imageUrl: "/images/article/article1.png",
    link: "https://www.holoniq.com/notes/holoniq-nordic-baltic-edtech-50",
    description: "SKIDOS – Selected as a part of the 2020 HolonIQ Nordic-Baltic EdTech 50",
  },
  {
    imageUrl: "/images/article/article2.png",
    link: "https://www.siliconrepublic.com/start-ups/copenhagen-startups-2018",
    description: "14 fast-moving Copenhagen start-ups to watch in 2018",
  },
  {
    imageUrl: "/images/article/article3.png",
    link: "https://finans.dk/erhverv/ECE10818976/adi-flyttede-til-danmark-og-aabnede-et-spilfirma-nu-har-han-faaet-77-mio-kr-paa-et-aar/?ctxref=ext",
    description: "Adi flyttede til Danmark og åbnede et spilfirma: Har fået 7,7 mio. kr. på et år",
  },
  {
    imageUrl: "/images/article/article4.png",
    link: "https://www.eu-startups.com/2018/09/10-european-video-game-startups-to-watch-in-2018-and-beyond/",
    description: "10 European video game startups to watch in 2018",
  },
  {
    imageUrl: "/images/article/article5.png",
    link: "https://www.huffpost.com/entry/9-teams-raise-the-bar-in_b_8112634",
    description: "9 Teams Raise the Bar in Startupbootcamp Mobile 2015",
  },
  {
    imageUrl: "/images/article/article6.png",
    link: "https://techcrunch.com/2017/08/28/skidos/",
    description: "SKIDOS on TechCrunch",
  },
  {
    imageUrl: "/images/article/article7.png",
    link: "https://finans.dk/tech/**********/adi-vil-goere-boern-klogere-mens-de-spiller-spil-paa-tablets-og-telefoner/?ctxref=ext",
    description: "Adi vil gøre børn klogere, mens de spiller spil på tablets og telefoner",
  },
  {
    imageUrl: "/images/article/article8.png",
    link: "https://yourstory.com/2015/05/skidos-startupbootcamp-mobile-copenhagen",
    description:
      "SKIDOS is the first Indian startup to be part of Startupbootcamp Mobile in Copenhagen",
  },
];

export const inforPopupData = [
  {
    name: "Create Player Section",
    bannerImage: "/images/webGl/whyInfoBanner/playerProfile.webp",
    heading: "WhypopupHeading",
    popupContent: ["PopupItem1", "PopupItem2", "PopupItem3"],
  },
  {
    name: "Personalise Player Section",
    bannerImage: "/images/webGl/whyInfoBanner/childExperience.webp",
    heading: "WhypopupHeading",
    popupContent: ["PopupItem1", "PopupItem2"],
  },
  {
    name: "Enter Email Section",
    bannerImage: "/images/webGl/whyInfoBanner/yourEmail.webp",
    heading: "WhypopupHeading",
    // popupContent: [
    //   "To provide information about your child's progress via email, keeping you updated and involved in their learning journey.",
    //   "To create a unique ID that ensures secure access to your SKIDOS account.",
    //   "To facilitate effective communication between parents and our educators, allowing for easy exchanges of feedback, questions, and updates."
    // ]
    popupContent: ["PopupItem1", "PopupItem2", "PopupItem3"],
  },
];

export const carouselDataGetStarted = [
  {
    bannerImage: "/images/webGl/getStartedBanner/1.webp",
    key: "CarouselSubText1",
  },
  {
    bannerImage: "/images/webGl/getStartedBanner/2.webp",
    key: "CarouselSubText2",
  },
  {
    bannerImage: "/images/webGl/getStartedBanner/3.webp",
    key: "CarouselSubText3",
  },
];
