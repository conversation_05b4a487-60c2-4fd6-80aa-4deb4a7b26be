.acquisitionContainerWrapper {
  max-width: 1728px;
  margin: 0 auto;
}
.plansContainer {
  display: flex;
  justify-content: center;
  gap: 20px; /* Space between cards */
}
.acquisitionHeader {
  /* height: 550px; */
  background-image: url("/images/acquisition/acquisitionPageHeader.webp");
  flex: 0 0 100%;
  height: 600px;
  background-size: cover;
  background-position: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  max-width: 1728px;
  margin: 0 auto;
}
.headerContentWrapper {
  width: 100%;
  height: 30%;
  color: #ffff;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 3rem;
  box-sizing: border-box;
}
.headerContentWrapper h1 {
  font-size: 2.5rem;
  margin: 0;
}
.headerContentWrapper p {
  font-family: var(--font-poppins);
  font-weight: 400;
  font-size: 1.3rem;
  margin: 0;
  padding: 0 10rem;
  box-sizing: border-box;
}
.headerImageWrapper {
  width: 100%;
  height: 70%;
  position: relative;
  width: 100%;
}
.mainContentWrapper {
  position: relative;
  top: -160px;
  text-align: center;
}

.plansCardWrapper {
  display: flex;
  justify-content: center;
  gap: 2rem;
}
.normalPlanCard {
  background-color: #ffff;
  border: 1px solid #d4d4d4;
  border-radius: 2rem;
  display: flex;
  flex-direction: column;
  text-align: center;
  align-items: center;
  padding: 1.4rem 1.4rem;
  align-self: flex-end;
  transition: transform 0.3s ease;
  min-width: 180px;
  box-sizing: border-box;
  width: 340px;
  height: 325px;
  justify-content: space-between;
}
.normalPlanCard:hover {
  cursor: pointer;
  transform: scale(1.02);
}

.normalPlanCardHeading {
  font-size: 1.9rem;
  margin: 0;
}
.discountWrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}
.originalPrice {
  color: rgba(0, 0, 0, 0.7);
  font-size: 1.4rem;
  text-decoration: line-through;
}
.discount {
  background-color: #1dc368;
  padding: 0.3rem 0.5rem;
  font-size: 1rem;
  border-radius: 0.7rem;
}
.discountedPrice {
  font-size: 1.9rem;
  margin: 0;
}
.billedYearly {
  font-size: 1rem;
  font-family: var(--font-poppins);
  font-weight: 500;
}
.activateBtn {
  width: 100%;
  /* border: 2px solid #4A2D80; */
  background-color: #eee5ff;
  border-radius: 0.8rem;
  padding: 0.8rem 0.5rem;
  font-size: 1.5rem;
  color: #4a2d80;
  font-family: var(--font-nevermind-bold);
  cursor: pointer;
  border: none;
}
.heroPlanCardWrapper {
  position: relative;
}
.heroPlanCardContentWrapper {
  width: 380px;
  height: 380px;
  background-image: url("/images/acquisition/heroCardBg.svg");
  background-size: cover;
  background-position: center;
  position: relative;
  z-index: 10;
  transition: transform 0.3s ease;
}
.heroPlanCardContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  margin: 0rem 2rem;
}
.bestValueContainer {
  background-color: #ffff;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 190px;
  padding: 0.7rem 0.8rem 0.7rem 0.8rem;
  top: -15px;
  border-radius: 20px;
  font-family: var(--font-poppins);
  font-weight: 500;
  font-size: 1.2rem;
  transition:
    top 0.3s ease-out,
    left 0.3s ease-out,
    padding 0.3s ease-out,
    border-radius 0.3s ease-out,
    width 0.3s ease-out;
}
.heroPlanCardWrapper:hover .bestValueContainer {
  top: -50px;
  /* padding: 0.5rem; */
  width: 150px;
  /* left: 120px; */
  border-radius: 40px;
}
.heroPlanCardContentWrapper:hover {
  transform: scale(1.02);
  cursor: pointer;
}
.heroHeading {
  font-size: 2.4rem;
  color: #ffff;
  margin: 0;
  margin-top: 1rem;
}
.heroDiscountedPrice {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.4rem;
  text-decoration: line-through;
}
.heroBilledYearly {
  font-size: 1.1rem;
  background-color: rgba(29, 195, 104, 1);
  padding: 0.25rem 0.5rem;
  color: #ffff;
  border-radius: 10px;
}
.heroPlanPrice {
  color: #ffff;
  font-size: 2.4rem;
  margin: 0;
}
.heroBilled {
  text-align: center;
  font-size: 1.1rem;
  font-family: var(--font-poppins);
  color: #fff;
  padding: 0;
}
.heroPlanBtn {
  width: 100%;
  border-radius: 1rem;
  background-color: #9258fe;
  color: #fff;
  padding: 0.8rem 0.5rem;
  font-size: 2rem;
  border: none;
  cursor: pointer;
  font-family: var(--font-nevermind-bold);
}
.allAboutContainer {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
  max-width: 90%;
  margin: 0 auto;
}
.allAboutContainer > div {
  width: 330px;
  text-align: center;
  font-family: var(--font-poppins);
  font-weight: 500;
  color: #023066;
}
.aboutCard > p {
  font-size: 1.5rem;
  font-family: var(--font-poppins);
  font-weight: 500;
}

.acquisitionHeading {
  margin: 4rem 0 2rem 0;
  font-size: 2rem;
}

.awardsWrapper {
  display: flex;
  flex-direction: column;
  margin: 1rem;
  justify-content: center;
  align-items: center;
  color: #ffff;
  text-align: center;
  padding-bottom: 3rem;
  gap: 20px;
  max-width: 100%;
  overflow: hidden;
}

.awardsWrapper > div {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
  max-width: 100%;
}

.awardImgWrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
  max-width: 100%;
}

.awardsWrapper h2 {
  font-size: 4.5rem;
}

.reviewWrapper {
  display: flex;
  justify-content: flex-start;
  gap: 0.5rem;
  margin: 1rem 1rem 0 1rem;
  text-align: center;
  overflow: auto;
  scrollbar-width: none;
  scroll-snap-type: x mandatory;
}
.reviewCard {
  padding: 1rem 0 0 0;
  border-radius: 8px;
  width: 300px;
  background-color: #f7f8fe;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  font-family: var(--font-poppins);
  color: #4a2d80;
  margin: 10px;
  border: 1.49px solid #d8deff;
}
.reviewCard p:nth-child(2) {
  margin: 1rem 2rem 0.5rem 2rem;
}
.reviewCardFooter {
  background-color: #ffff;
  width: 300px;
  border-radius: 0px 0px 8px 8px;
  font-size: 0.8rem;
}

@media only screen and (min-width: 900px) and (max-width: 1100px) {
  .mainContentWrapper {
    top: -180px;
  }
  .plansCardWrapper {
    gap: 1.5rem;
  }
  .normalPlanCard {
    border-radius: 1.8rem;
    padding: 1.2rem 1.2rem;
    /* width: 280px; */
    width: 260px;
    height: 285px;
  }
  .normalPlanCardHeading {
    font-size: 1.5rem;
  }
  .discountWrapper {
    gap: 5px;
  }
  .originalPrice {
    font-size: 1rem;
  }
  .discount {
    padding: 0.3rem 0.5rem;
    font-size: 0.8rem;
    border-radius: 0.7rem;
  }
  .discountedPrice {
    font-size: 1.5rem;
  }
  .billedYearly {
    font-size: 1rem;
  }
  .activateBtn {
    border-radius: 0.6rem;
    padding: 0.5rem 0.3rem;
    font-size: 1.2rem;
  }
  .heroPlanCardContentWrapper {
    width: 320px;
    height: 320px;
  }
  .bestValueContainer {
    left: 50%;
    transform: translateX(-50%);
    width: 150px;
    padding: 0.6rem 0.8rem 0.6rem 0.8rem;
    top: -15px;
    border-radius: 20px;
    border-radius: 20px;
    font-size: 1rem;
  }
  .heroPlanCardWrapper:hover .bestValueContainer {
    width: 110px;
  }
  .heroHeading {
    font-size: 2rem;
    color: #ffff;
    margin: 0;
    margin-top: 1rem;
  }
  .heroDiscountedPrice {
    font-size: 1.2rem;
  }
  .heroBilledYearly {
    font-size: 1rem;
    padding: 0.3rem 0.5rem;
    border-radius: 10px;
  }
  .heroPlanPrice {
    color: #ffff;
    font-size: 2rem;
    margin: 0;
  }
  .heroBilled {
    text-align: center;
    font-size: 0.8rem;
    font-family: var(--font-poppins);
    color: #fff;
    padding: 0;
  }
  .heroPlanBtn {
    padding: 0.5rem 0.5rem;
    font-size: 1.5rem;
    border-radius: 0.8rem;
  }
}

@media only screen and (min-width: 600px) and (max-width: 900px) {
  .mainContentWrapper {
    top: -120px;
  }
  .plansCardWrapper {
    gap: 0.7rem;
  }
  .normalPlanCard {
    border-radius: 1.3rem;
    padding: 1rem 1rem;
    width: 200px;
    height: 205px;
  }
  .normalPlanCardHeading {
    font-size: 1rem;
  }
  .discountWrapper {
    gap: 5px;
  }
  .originalPrice {
    font-size: 0.7rem;
  }
  .discount {
    padding: 0.2rem 0.3rem;
    font-size: 0.6rem;
    border-radius: 0.5rem;
  }
  .discountedPrice {
    font-size: 1rem;
  }
  .billedYearly {
    font-size: 0.7rem;
  }
  .activateBtn {
    border-radius: 0.5rem;
    padding: 0.4rem 0.2rem;
    font-size: 0.8rem;
  }
  .heroPlanCardContentWrapper {
    width: 256px;
    height: 256px;
  }
  .heroPlanCardContent {
    margin: 0rem 1.5rem;
  }
  .bestValueContainer {
    width: 120px;
    padding: 0.6rem 0.8rem 0.6rem 0.8rem;
    top: -15px;
    font-size: 0.8rem;
  }
  .heroPlanCardWrapper:hover .bestValueContainer {
    top: -35px;
    width: 90px;
  }
  .heroHeading {
    font-size: 1.5rem;
    color: #ffff;
    margin: 0;
    margin-top: 1rem;
  }
  .heroDiscountedPrice {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1rem;
    text-decoration: line-through;
  }
  .heroBilledYearly {
    font-size: 0.8rem;
    padding: 0.2rem 0.4rem;
    border-radius: 8px;
  }
  .heroPlanPrice {
    font-size: 1.5rem;
  }
  .heroBilled {
    font-size: 0.8rem;
  }
  .heroPlanBtn {
    padding: 0.5rem 0.3rem;
    font-size: 1.2rem;
    border-radius: 0.5rem;
  }
  .aboutContImg {
    width: 280px;
    height: 200px;
  }
}

@media only screen and (min-width: 600px) and (max-width: 900px) {
  .acquisitionHeader {
    height: 400px;
  }
  .headerContentWrapper h1 {
    font-size: 2rem;
  }
  .headerContentWrapper p {
    font-size: 1.4rem;
    padding: 0;
    /* padding: 0 4rem */
  }
}

@media only screen and (max-width: 600px) {
  .acquisitionHeader {
    background-image: url("/images/acquisition/acquisitionPageHeaderMb.webp");
    height: 400px;
  }
  .plansContainer {
    flex-direction: column;
  }
  .headerContentWrapper h1 {
    font-size: 1.8rem;
  }
  .headerContentWrapper p {
    font-size: 1.2rem;
    padding: 0.5rem;
  }
  .headerContentWrapper {
    height: 40%;
    box-sizing: border-box;
    padding: 1rem;
    margin-top: 1rem;
  }
  .headerImageWrapper {
    height: 60%;
  }
  .mainContentWrapper {
    top: -80px;
  }
  .plansCardWrapper {
    flex-direction: column;
  }
  .normalPlanCard {
    align-self: center;
    order: 2;
    min-width: 250px;
    width: 270px;
    height: 265px;
  }
  .heroPlanCardWrapper {
    display: flex;
    justify-content: center;
    order: 1;
  }
  .bestValueContainer {
    left: 50%;
    transform: translateX(-50%);
    width: 150px;
    text-align: center;
    top: -10px;
    padding: 0.5rem 0.7rem 1rem 0.7rem;
    border-radius: 20px;
    font-size: 1.2rem;
  }
  .heroPlanCardWrapper:hover .bestValueContainer {
    width: 120px;
    left: 50%;
    top: -35px;
    border-radius: 40px;
    padding: 0.5rem 0.6rem 0.5rem 0.6rem;
  }
  .heroPlanCardContentWrapper {
    width: 300px;
    height: 300px;
  }
  .heroHeading {
    font-size: 2rem;
  }
  .heroDiscountedPrice {
    font-size: 1.5rem;
  }
  .heroBilledYearly {
    font-size: 1.1rem;
    padding: 0.3rem 0.5rem;
  }
  .heroPlanPrice {
    font-size: 2rem;
  }
  .heroBilled {
    font-size: 1.2rem;
  }
  .heroPlanBtn {
    border-radius: 0.8rem;
    padding: 0.9rem 0.5rem;
    font-size: 1.5rem;
  }
  .allAboutContainer {
    gap: 28px;
  }
}

@media only screen and (max-width: 1024px) {
  .awardsWrapper > div {
    gap: 20px;
  }
  .awardImgWrapper img {
    width: 80px;
    height: auto;
  }
}

@media only screen and (max-width: 768px) {
  .awardsWrapper > div {
    flex-wrap: wrap;
    justify-content: center;
  }
  .awardImgWrapper img {
    width: 70px;
    height: auto;
  }
}

@media only screen and (max-width: 600px) {
  .awardsWrapper > div {
    flex-direction: column;
    align-items: center;
  }
  .awardImgWrapper img {
    width: 60px;
    height: auto;
  }
}
