/* Product Page Wrapper */
.productPageWrapper {
  font-family: var(--font-poppins);
  background: #ffffff;
}

.productWrapperInner {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 60px;
  text-align: center;
}

/* Hero Section */
.heroSection {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  padding: 20px 0;
  position: relative;
  overflow: hidden;
}

.heroContent {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 60px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.heroLeft {
  z-index: 2;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.gameTitle {
  font-size: 4rem;
  font-weight: 900;
  color: #000;
  margin: 0;
  line-height: 1.2;
  font-family: var(--font-poppins);
}

.gameDescription {
  font-size: 2rem;
  color: #424242;
  margin: 0;
  line-height: 1.5;
  font-weight: 500;
  opacity: 0.9;
}

/* Store Icons Section */
.productPageStoreIcons {
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  max-width: 26rem;
  margin-right: auto;
    padding-left: 0;
}

/* Rating Section */
.ratingSection {
  margin-top: 8px;
}

.ratingStars {
  display: flex;
  align-items: center;
  gap: 12px;
}

.ratingNumber {
  font-size: 1.8rem;
  font-weight: 600;
  color: #ffffff;
}

.stars {
  display: flex;
  gap: 4px;
}

.starFilled {
  color: #FFD700;
  font-size: 1.4rem;
}

.starEmpty {
  color: rgba(255, 255, 255, 0.3);
  font-size: 1.4rem;
}

.reviewCount {
  font-size: 1rem;
  color: #ffffff;
  opacity: 0.8;
}

/* Hero Right */
.heroRight {
  display: flex;
  justify-content: center;
  align-items: center;
}

.gameImageWrapper {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.gameImage {
  width: 100%;
  height: 32rem;
  border-radius: 15px;
}

/* Video and About Section */
.videoAboutSection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  margin: 60px 0;
  align-items: center;
}

.videoContainer {
  position: relative;
  width: 100%;
  aspect-ratio: 16/9;
  border-radius: 16px;
  overflow: hidden;
  background: #f8f9fa;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

/* About Game Section */
.aboutGameSection {
  padding: 0;
}

.aboutGameTitle {
  font-size: 2.2rem;
  font-weight: 700;
  color: #2d3436;
  margin: 0 0 32px 0;
  line-height: 1.3;
  text-align: left;
}

.aboutGamePoints {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
}


.aboutGamePoint {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  font-size: 1.5rem; /* Matches the larger text in the image */
  line-height: 1.4;
  color: #444444;
  text-align: left;
  padding: 12px 0;
}

.iconPlaceholder {
  width: 4rem;
  height: 4rem;
  background-color: #eaeaea; /* Light gray like in the image */
  border-radius: 8px; /* Rounded square */
  flex-shrink: 0;
  margin-top: 4px; /* Vertically aligns the box with the text top */
}

.aboutGameDescription {
  margin: 60px 0;
  padding: 0;
}

.descriptionText {
  font-size: 1.2rem;
  line-height: 1.8;
  color: #2d3436;
  margin: 0;
  text-align: justify;
}

.videoPlaceholder {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.playButton {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.playButton:hover {
  transform: scale(1.1);
  background: rgba(255, 255, 255, 1);
}

.playIcon {
  font-size: 24px;
  color: #333;
  margin-left: 4px;
}

.videoOverlay {
  position: absolute;
  bottom: 20px;
  left: 20px;
  color: white;
  font-weight: 500;
}

/* Learning Section */
.learningSection {
  margin: 80px 0;
}

.learningSectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3436;
  margin: 0 0 32px 0;
  line-height: 1.3;
}

.learningDescription {
  font-size: 1.2rem;
  line-height: 1.8;
  color: #636e72;
  margin-bottom: 48px;
  text-align: justify;
}

.learningOutcomes {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
  margin-bottom: 48px;
}

.learningCard {
  text-align: center;
  padding: 32px 24px;
  border-radius: 16px;
  transition: transform 0.3s ease;
}

.learningCard:hover {
  transform: translateY(-4px);
}

.learningIconPlaceholder {
  width: 80px;
  height: 80px;
  background-color: rgba(116, 185, 255, 0.1);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 24px auto;
  font-size: 2rem;
}

.learningCardTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2d3436;
  margin: 0;
  line-height: 1.4;
}

/* Age Section */
.ageSection {
  margin: 80px 0;
}

.ageSectionTitle {
  font-size: 2.2rem;
  font-weight: 700;
  color: #2d3436;
  margin: 0 0 24px 0;
  line-height: 1.3;
}

.ageSectionDescription {
  font-size: 1.2rem;
  line-height: 1.8;
  color: #636e72;
  margin: 0;
  text-align: justify;
}

/* Screenshots Section */
.screenshotsSection {
  display: flex;
  gap: 20px;
  margin-top: 40px;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 10px 0;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  scrollbar-color: #ccc transparent;
}

.screenshotsSection::-webkit-scrollbar {
  height: 6px;
}

.screenshotsSection::-webkit-scrollbar-track {
  background: transparent;
}

.screenshotsSection::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 3px;
}

.screenshotsSection::-webkit-scrollbar-thumb:hover {
  background-color: #999;
}

.screenshotWrapper {
  flex: 0 0 auto;
  width: 200px;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.screenshot {
  width: 100%;
  height: auto;
  border-radius: 15px;
}

/* Reviews Section */
.reviewsSection {
  margin: 80px 0;
}

.reviewsSectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3436;
  margin: 0 0 48px 0;
  line-height: 1.3;
  text-align: center;
}

.reviewsGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  place-items: center;
}

.reviewCard {
  background: #F7F8FE;
  border-radius: 12px;
  padding: 24px;
  border: none;
  transition: transform 0.3s ease;
  text-align: left;
  display: flex;
  flex-direction: column;
  min-height: 280px;
  align-items: center;
}

.reviewCard:hover {
  transform: translateY(-2px);
}

.stars {
  display: flex;
  gap: 2px;
  margin-bottom: 16px;
}

.starFilled {
  color: #FFD700;
  font-size: 1.1rem;
}

.starEmpty {
  color: #d1d5db;
  font-size: 1.1rem;
}

.reviewContent {
  font-size: 0.9rem;
  line-height: 1.5;
  color: #4A2D80;
  margin: 0;
  text-align: left;
  flex-grow: 1;
  margin-bottom: 16px;
}

.reviewHeader {
  margin-top: auto;
}

.reviewAuthor {
  font-size: 0.85rem;
  font-weight: 600;
  color: #6b46c1;
  margin: 0;
  background: #E5E9FF;
  padding: 1rem;
}

/* VSPs Section */
.vspsSection {
  margin: 80px 0;
}

.vspsGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

.vspCard {
  text-align: center;
}

.vspCardContent {
text-align: center;
    padding: 4rem;
    background: #e1f5fe;
    border-radius: 12px;
    border: none;
    transition: transform 0.3s ease;
  justify-items: center;
}

.vspCard:hover {
  transform: translateY(-2px);
}

.vspIcon {
  width: 80px;
  height: 80px;
  background-color: #ffffff;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 2.2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.vspTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3436;
  margin-top: 1rem;
  line-height: 1.3;
}

/* FAQs Section */
.faqsSection {
  margin: 80px 0;
}

.faqsSectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3436;
  margin: 0 0 48px 0;
  line-height: 1.3;
  text-align: center;
}

.faqsList {
  max-width: 700px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.faqItem {
  background: #f5f5f5;
  border-radius: 8px;
  border: none;
  overflow: hidden;
}

.faqQuestion {
  padding: 18px 20px;
  font-size: 0.95rem;
  font-weight: 500;
  color: #333333;
  cursor: pointer;
  transition: background-color 0.3s ease;
  list-style: none;
  position: relative;
  background-color: #f5f5f5;
  margin: 0;
  text-align: left;
}

.faqQuestion:hover {
  background-color: #eeeeee;
}

.faqQuestion::after {
  content: "▼";
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.7rem;
  color: #666666;
  transition: transform 0.3s ease;
}

.faqItem[open] .faqQuestion::after {
  transform: translateY(-50%) rotate(180deg);
}

.faqAnswer {
  padding: 0 20px 18px 20px;
  background-color: #f5f5f5;
}

.faqAnswer p {
  font-size: 0.9rem;
  line-height: 1.5;
  color: #555555;
  margin: 0;
  text-align: left;
}

/* Related Games Section */
.relatedGamesSection {
  margin: 80px 0;
}

.relatedGamesSectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3436;
  margin: 0 0 48px 0;
  line-height: 1.3;
  text-align: center;
}

.relatedGamesGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
}

.relatedGameCard {
  text-align: center;
  background: #ffffff;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  transition: transform 0.3s ease;
}

.relatedGameCard:hover {
  transform: translateY(-4px);
}

.relatedGameImageWrapper {
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
}

.relatedGameImage {
  width: 100%;
  height: auto;
  display: block;
}

.relatedGameName {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2d3436;
  margin: 0 0 20px 0;
  line-height: 1.3;
}

.relatedGameDescription {
  font-size: 1rem;
  color: #636e72;
  margin: 12px 0;
  line-height: 1.4;
  text-align: center;
}

.relatedGameTags {
  display: flex;
  justify-content: center;
  margin: 12px 0;
}

.skillTag {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 500;
}

.learnMoreButton {
  background-color: #74b9ff;
  color: white;
  border: none;
  padding: 8px 24px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.learnMoreButton:hover {
  background-color: #0984e3;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .productWrapperInner {
    padding: 0 20px;
  }

  .heroSection {
    padding: 40px 0;
  }

  .heroContent {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
    padding: 0 20px;
  }

  .gameTitle {
    font-size: 4rem;
  }

  .gameDescription {
    font-size: 1.8rem;
  }

  .ratingNumber {
    font-size: 2rem;
  }

  .starFilled, .starEmpty {
    font-size: 1.6rem;
  }

  .reviewCount {
    font-size: 1.2rem;
  }

  .videoAboutSection {
    grid-template-columns: 1fr;
    gap: 40px;
    margin: 40px 0;
  }

  .aboutGameTitle {
    font-size: 2.8rem;
  }

  .aboutGamePoint {
    font-size: 1.8rem;
  }

  .descriptionText {
    font-size: 1.5rem;
  }

  .learningSection {
    margin: 60px 0;
  }

  .learningSectionTitle {
    font-size: 2.8rem;
  }

  .learningDescription {
    font-size: 1.5rem;
  }

  .learningCardTitle {
    font-size: 1.5rem;
  }

  .learningOutcomes {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .ageSection {
    margin: 60px 0;
  }

  .ageSectionTitle {
    font-size: 2.5rem;
  }

  .ageSectionDescription {
    font-size: 1.5rem;
  }

  .screenshotsSection {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin: 60px 0;
  }

  .reviewsSection {
    margin: 60px 0;
  }

  .reviewsSectionTitle {
    font-size: 2.8rem;
  }

  .reviewsGrid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .reviewCard {
    padding: 20px;
    min-height: 240px;
  }

  .reviewContent {
    font-size: 1.2rem;
  }

  .reviewAuthor {
    font-size: 1.1rem;
  }

  .vspsSection {
    margin: 60px 0;
  }

  .vspsGrid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .vspCard {
    padding: 28px 16px;
  }

  .vspIcon {
    width: 70px;
    height: 70px;
    font-size: 2rem;
  }

  .vspTitle {
    font-size: 1.4rem;
  }

  .faqsSection {
    margin: 60px 0;
  }

  .faqsSectionTitle {
    font-size: 2.8rem;
  }

  .faqQuestion {
    padding: 16px 18px;
    font-size: 1.2rem;
  }

  .faqAnswer {
    padding: 0 18px 16px 18px;
  }

  .faqAnswer p {
    font-size: 1.1rem;
  }

  .relatedGamesSection {
    margin: 60px 0;
  }

  .relatedGamesSectionTitle {
    font-size: 2.8rem;
  }

  .relatedGamesGrid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .gameName {
    font-size: 1.8rem;
  }

  .gameDescription {
    font-size: 1.5rem;
  }

  .gameSkillsWrapper p {
    font-size: 1.1rem;
  }

  .gameCardBtn {
    font-size: 1.4rem;
  }

  .relatedGameName {
    font-size: 1.5rem;
  }

  .relatedGameDescription {
    font-size: 1.3rem;
  }

  .skillTag {
    font-size: 1rem;
  }

  .learnMoreButton {
    font-size: 1.2rem;
  }
}

/* Tablet Responsive */
@media (max-width: 1024px) and (min-width: 769px) {
  .productWrapperInner {
    padding: 0 40px;
  }

  .videoAboutSection {
    gap: 30px;
  }

  .aboutGameSection {
    min-width: 250px;
  }

  .videoPlaceholder {
    min-width: 250px;
    min-height: 300px;
  }

  .gameDescriptions {
    min-width: 250px;
  }

  .videoContainer {
    min-width: 250px;
    height: 300px;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .productWrapperInner {
    padding: 0 15px;
  }

  .gameTitle {
    font-size: 3.8rem;
  }

  .gameDescription {
    font-size: 1.6rem;
  }

  .heroSection {
    padding: 20px 15px;
  }

  .videoAboutSection {
    gap: 20px;
  }

  .aboutGameTitle {
    font-size: 2.2rem;
  }

  .aboutGamePoint {
    font-size: 1.6rem;
  }

  .descriptionText {
    font-size: 1.3rem;
  }

  .learningSectionTitle {
    font-size: 2.4rem;
  }

  .learningDescription {
    font-size: 1.3rem;
  }

  .learningCardTitle {
    font-size: 1.3rem;
  }

  .ageSectionTitle {
    font-size: 2.2rem;
  }

  .ageSectionDescription {
    font-size: 1.3rem;
  }

  .reviewsSectionTitle {
    font-size: 2.4rem;
  }

  .reviewContent {
    font-size: 1.1rem;
  }

  .reviewAuthor {
    font-size: 1rem;
  }

  .vspTitle {
    font-size: 1.2rem;
  }

  .faqsSectionTitle {
    font-size: 2.4rem;
  }

  .faqQuestion {
    font-size: 1.1rem;
  }

  .faqAnswer p {
    font-size: 1rem;
  }

  .relatedGamesSectionTitle {
    font-size: 2.4rem;
  }

  .gameName {
    font-size: 1.6rem;
  }

  .gameDescription {
    font-size: 1.3rem;
  }

  .gameSkillsWrapper p {
    font-size: 1rem;
  }

  .gameCardBtn {
    font-size: 1.2rem;
  }

  .videoPlaceholder {
    min-height: 300px;
  }
}



.gameCardsWrapper {
  display: grid;
  justify-items: center;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
  font-family: var(--font-poppins);
  margin-bottom: 6rem;
}
.gameName {
  font-weight: 700;
  font-size: 1.5rem;
  margin: 0;
}
.gameDescription {
  font-weight: 400;
  font-size: 1.3rem;
  color: rgba(0, 0, 0, 0.5);
  margin: 0;
}
.gameSkillsWrapper {
  display: flex;
  gap: 5px;
}
.gameSkillsWrapper p {
  font-size: 0.9rem;
  background-color: #f7f7f7;
  padding: 0.5rem 1rem 0.5rem 1rem;
  border-radius: 2.5rem;
  border: 1px solid #e9e9e9;
}

.productCards {
  padding: 1rem;
  border-radius: 1rem;
  border: 1px solid rgba(217, 217, 217, 1);
  background-color: #ffffff;
  max-width: 350px;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.15);
  /* transition: box-shadow 0.3s ease-in-out, transform 0.3s ease-in-out;  */
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.productCards:hover {
  box-shadow: 0 0 12px rgba(0, 0, 0, 0);
  transform: scale(0.98);
}
.gameCardsImg {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 20px;
}

.gameCardBtn {
  width: 100%;
  margin: 1rem 0;
  background-color: transparent;
  font-size: 1.2rem;
  color: black;
  border: 1px solid #b0b0b0;
  border-radius: 0.5rem;
  padding: 0.8rem 2rem;
  text-decoration: none;
  cursor: pointer;
}
.gameCardBtn:hover {
  color: #ffff;
  background-color: black;
  border: 1px solid black;
}