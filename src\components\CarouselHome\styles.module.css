.carouselWrapper {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.carousel {
  display: flex;
  transition: transform 0.3s ease-in-out;
  width: 100%;
}

.slide {
  flex: 0 0 100%;
  height: 600px;
  background-size: cover;
  background-position: center;
}

.carouselContentWrapper {
  display: flex;
  align-items: center;
  height: 100%;
}
.carouselTextContentWrapper {
  width: 55%;
  padding: 0 5rem;
  box-sizing: border-box;
}
.carouselTextContentWrapper h1 {
  color: #ffff;
  font-size: 3rem;
  margin: 0;
}
.carouselTextContentWrapper p {
  margin: 0.8rem 0;
  color: #fff;
  font-size: 1.5rem;
  font-family: var(--font-poppins);
  font-weight: 500;
}
.carouselTextContentWrapper button {
  margin-top: 1rem;
  background-color: #9258fe;
  font-size: 2rem;
  font-weight: 500;
  color: #ffff;
  border-radius: 1rem;
  padding: 1rem 3.1rem;
  text-decoration: none;
  border: none;
  box-shadow: 0px 10px 0px rgba(74, 45, 128, 1);
  cursor: pointer;
  font-family: var(--font-poppins);
}
.carouselImgContentWrapper {
  position: relative;
  width: 45%;
  height: 100%;
}

.active {
  opacity: 1;
}

.indicators {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  border: 1px solid #ffff;
  padding: 6px;
  border-radius: 40px;
  box-sizing: border-box;
  z-index: 50;
}

.indicator {
  width: 8px;
  height: 8px;
  background-color: #fff;
  opacity: 1;
  border-radius: 50%;
  transition:
    width 0.9s,
    height 0.9s,
    border-radius 0.9s,
    background-color 0.9s;
  margin: 0 5px;
  background-color: rgba(255, 255, 255, 0.3);
  cursor: pointer;
}

.indicator.active {
  width: 40px;
  height: 8px;
  background-color: #ffff;
  border-radius: 24px;
  transition:
    width 0.9s,
    height 0.9s,
    border-radius 0.9s,
    background-color 0.9s;
}

@media only screen and (min-width: 768px) and (max-width: 1023px) {
  .carouselTextContentWrapper {
    padding: 0 1rem;
  }
  .carouselTextContentWrapper h1 {
    font-size: 2.5rem;
  }
  .carouselTextContentWrapper p {
    font-size: 1.25rem;
  }
  .carouselTextContentWrapper button {
    font-size: 1.75rem;
  }
  .carouselImgContentWrapper {
    height: 400px;
  }
}

@media only screen and (max-width: 767px) {
  .carouselContentWrapper {
    flex-direction: column;
  }
  .carouselTextContentWrapper {
    width: 100%;
    padding: 1.6rem 1rem;
    text-align: center;
  }
  .carouselTextContentWrapper h1 {
    font-size: 1.8rem;
  }
  .carouselTextContentWrapper p {
    font-size: 0.9rem;
  }
  .carouselTextContentWrapper button {
    font-size: 1.3rem;
    border-radius: 0.6rem;
    padding: 0.6rem 2rem;
  }
  .carouselImgContentWrapper {
    width: 100%;
  }
}
