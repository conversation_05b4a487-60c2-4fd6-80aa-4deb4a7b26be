"use client";

import StoreIcons from "@/components/Footer/StoreIcons";
import dynamic from "next/dynamic";
import Image from "next/image";
import "react-responsive-carousel/lib/styles/carousel.min.css";
import "./rekindle.css";

// Dynamically import Plyr to avoid SSR issues
const Plyr = dynamic(() => import("plyr-react"), {
  ssr: false,
  loading: () => <div>Loading video player...</div>,
});

import { useAuth } from "@/context/AuthContext";
import { trackWebEngageEvent } from "@/utils/webengage";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import "plyr-react/plyr.css";

function RekindleMinds() {
  const controls = [
    "play-large",
    "play",
    "progress",
    "current-time",
    "mute",
    "captions",
    "pip",
    "airplay",
    "fullscreen",
  ];

  const t = useTranslations("Footer");
  const tPage = useTranslations("EmotionalWellBeingPage");
  const { isSubscribed } = useAuth();

  const router = useRouter();

  const icons = [
    {
      src: "/images/footer/socialIcons/Amazon.webp",
      alt: t("Amazon"),
      onClick: () => window.open("https://www.amazon.com/gp/product/B0DH6RT2JV", "_blank"),
    },
    {
      src: "/images/footer/socialIcons/Appstore.webp",
      alt: t("Appstore"),
      onClick: () =>
        window.open(
          "https://apps.apple.com/us/app/skidos-learning-games-for-kids/id1483744837",
          "_blank"
        ),
    },
    {
      src: "/images/footer/socialIcons/Playstore.webp",
      alt: t("Playstore"),
      onClick: () =>
        window.open(
          "https://play.google.com/store/apps/details?id=skidos.shopping.toddler.learning.games&hl=en_IN",
          "_blank"
        ),
    },
    {
      src: "/images/footer/socialIcons/Web.webp",
      alt: t("Web"),
      onClick: () => handleWebIcon(),
    },
  ];
  const handleWebIcon = () => {
    trackWebEngageEvent("WebGLFooterIconClk");

    if (isSubscribed) {
      router.push("/user-home-screen");
    } else {
      router.push("/get-started");
    }
  };

  return (
    <div className="rekindle-minds">
      <div className="hero">
        <div className="container">
          <div className="row flex-column align-items-center d-flex justify-content-center header">
            <div
              className="col-12 col-md-8 text-center"
              style={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <h1 className="headingh1">{tPage("hero.title")}</h1>
              <h1 className="subheadingh1">{tPage("hero.subtitle")}</h1>

              <StoreIcons
                icons={icons}
                styleClass="align-items-center justify-content-center align-content-center"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="container" id="player-container">
        <div className="row d-flex justify-content-center">
          <div className="col-12 col-md-8">
            <div className="player-container">
              <Plyr
                id="plyr"
                controls
                options={{ volume: 0.1, controls }}
                source={{
                  type: "video",
                  sources: [
                    {
                      src: "https://www.youtube.com/watch?v=-ESpMGSAFFI",
                      provider: "youtube",
                    },
                  ],
                }}
              />
            </div>
          </div>
        </div>

        <div className="row container-block mt-7">
          <div className="col-12 col-md-6 block-img d-flex justify-content-start">
            <Image
              src="/images/rekindle-minds/why-play.png"
              alt={tPage("sections.whyPlay.imageAlt")}
              width={500}
              height={300}
            />
          </div>
          <div className="col-12 col-md-6">
            <h2>{tPage("sections.whyPlay.title")}</h2>
            <p>{tPage("sections.whyPlay.description")}</p>
          </div>
        </div>

        <div className="row container-block mt-7">
          <div className="col-12 col-md-6">
            <h2>{tPage("sections.playtimeWithPurpose.title")}</h2>
            <p>{tPage("sections.playtimeWithPurpose.description")}</p>
          </div>
          <div className="col-12 col-md-6 block-img d-flex justify-content-end">
            <Image
              src="/images/rekindle-minds/how-it-works.png"
              alt={tPage("sections.playtimeWithPurpose.imageAlt")}
              width={500}
              height={300}
            />
          </div>
        </div>
      </div>

      <div className="ribbon-block">
        <div className="ribbon"></div>
      </div>

      <div className="container rekindle-minds mt-7" style={{ marginBottom: "4rem" }}>
        <div className="row container-block">
          <div className="col-12 col-md-6 block-img d-flex justify-content-start">
            <Image
              src="/images/rekindle-minds/progress-report.png"
              alt={tPage("sections.expertApproved.imageAlt")}
              width={500}
              height={300}
            />
          </div>
          <div className="col-12 col-md-6">
            <h2>{tPage("sections.expertApproved.title")}</h2>
            <p>{tPage("sections.expertApproved.description")}</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default RekindleMinds;
