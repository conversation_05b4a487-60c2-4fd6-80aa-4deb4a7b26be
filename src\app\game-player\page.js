"use client";
import ScreenRotationOverlay from "@/components/prompt/page";
import { trackWebEngageEvent } from "@/utils/webengage";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";

const GamePlayer = ({ searchParams }) => {
  const [isPortableDevice, setIsPortableDevice] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const router = useRouter();
  const gameUrl = searchParams.gameUrl || "";
  const gameName = searchParams.gameName || "Game";
  const gameParamsOrientation = searchParams.gameOrientation || "portrait";
  const containerRef = useRef(null);
  const [gameOrientation] = useState(gameParamsOrientation);
  const [currentOrientation, setCurrentOrientation] = useState("portrait");

  useEffect(() => {
    trackWebEngageEvent("WebGlGameScrReached");
  }, []);

  // Helper functions defined before hooks
  const getFullscreenElement = () => {
    return (
      document.fullscreenElement ||
      document.webkitFullscreenElement ||
      document.mozFullscreenElement ||
      document.msFullscreenElement
    );
  };

  const requestFullscreen = async (element) => {
    if (element.requestFullscreen) {
      return element.requestFullscreen();
    } else if (element.webkitRequestFullscreen) {
      return element.webkitRequestFullscreen();
    } else if (element.mozRequestFullScreen) {
      return element.mozRequestFullScreen();
    } else if (element.msRequestFullscreen) {
      return element.msRequestFullscreen();
    }
  };

  const exitFullscreen = async () => {
    if (document.exitFullscreen) {
      return document.exitFullscreen();
    } else if (document.webkitExitFullscreen) {
      return document.webkitExitFullscreen();
    } else if (document.mozCancelFullScreen) {
      return document.mozCancelFullScreen();
    } else if (document.msExitFullscreen) {
      return document.msExitFullscreen();
    }
  };

  // All useEffect hooks grouped together
  useEffect(() => {
    const checkPortableDevice = () => {
      const userAgent = navigator.userAgent.toLowerCase();
      const isPad = /ipad/.test(userAgent);
      const isTablet = /(tablet|tab|playbook)|(android(?!.*mobile))/.test(userAgent);
      const isMobile = /mobile|ip(hone|od)|android/.test(userAgent);
      const isModernIPad =
        navigator.maxTouchPoints > 1 && /macintosh/.test(userAgent) && "ontouchend" in document;
      const isSamsungTablet = /sm-t|galaxy tab/i.test(userAgent);

      setIsPortableDevice(isPad || isTablet || isMobile || isModernIPad || isSamsungTablet);
    };

    checkPortableDevice();
    window.addEventListener("resize", checkPortableDevice);
    return () => window.removeEventListener("resize", checkPortableDevice);
  }, [gameUrl]);

  useEffect(() => {
    if (isPortableDevice && gameUrl && containerRef.current) {
      const enterFullscreen = async () => {
        try {
          await requestFullscreen(containerRef.current);
          if (window.screen.orientation && window.screen.orientation.lock) {
            try {
              await window.screen.orientation.lock(gameOrientation);
            } catch (err) {
              console.error("Screen orientation lock failed:", err);
            }
          }
        } catch (err) {
          console.error("Fullscreen request failed:", err);
        }
      };
      enterFullscreen();
    }

    return () => {
      if (isFullscreen) {
        try {
          exitFullscreen();
        } catch (error) {
          console.error("Exit fullscreen failed:", error);
        }
      }
      if (window.screen.orientation && window.screen.orientation.unlock) {
        window.screen.orientation.unlock();
      }
    };
  }, [isPortableDevice, gameUrl, gameOrientation, isFullscreen]);

  useEffect(() => {
    const navbar = document.getElementById("navbar");
    const footer = document.getElementById("footer");
    const cookieWidget = document.getElementById("CookiebotWidget");
    const rfLauncher = document.getElementById("rf-launcher");

    // Hide elements
    if (navbar) navbar.style.display = "none";
    if (footer) footer.style.display = "none";
    if (cookieWidget) cookieWidget.style.display = "none";
    if (rfLauncher) rfLauncher.style.display = "none";

    return () => {
      // Show elements when component unmounts
      if (navbar) navbar.style.display = "flex";
      if (footer) footer.style.display = "block";
      if (cookieWidget) cookieWidget.style.display = "block";
      if (rfLauncher) rfLauncher.style.display = "block";
    };
  }, []);

  useEffect(() => {
    const updateOrientation = () => {
      setCurrentOrientation(window.innerWidth > window.innerHeight ? "landscape" : "portrait");
    };
    updateOrientation();
    window.addEventListener("resize", updateOrientation);
    return () => window.removeEventListener("resize", updateOrientation);
  }, []);

  useEffect(() => {
    const handleFullscreenChange = () => {
      try {
        setIsFullscreen(!!getFullscreenElement());
      } catch (error) {
        console.error("Fullscreen change detection failed:", error);
      }
    };

    document.addEventListener("fullscreenchange", handleFullscreenChange);
    document.addEventListener("webkitfullscreenchange", handleFullscreenChange);
    document.addEventListener("mozfullscreenchange", handleFullscreenChange);
    document.addEventListener("MSFullscreenChange", handleFullscreenChange);

    return () => {
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
      document.removeEventListener("webkitfullscreenchange", handleFullscreenChange);
      document.removeEventListener("mozfullscreenchange", handleFullscreenChange);
      document.removeEventListener("MSFullscreenChange", handleFullscreenChange);
    };
  }, []);

  // Style functions
  const getContainerStyle = () => ({
    position: "fixed",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    display: "flex",
    flexDirection: "column",
    backgroundColor: "#000",
    overflow: "hidden",
  });

  const getIframeContainerStyle = () => ({
    flex: 1,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
    overflow: "hidden",
  });

  const getIframeStyle = () => {
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;
    const bottomBarHeight = !isPortableDevice ? 60 : 0;
    const availableHeight = screenHeight - bottomBarHeight;

    let width, height;

    if (gameOrientation === "portrait") {
      const gameAspectRatio = 9 / 16;
      width = screenWidth;
      height = width / gameAspectRatio;

      if (height > availableHeight) {
        height = availableHeight;
        width = height * gameAspectRatio;
      }
    } else {
      if (isPortableDevice) {
        width = screenWidth * 0.98;
        height = width / (16 / 9);

        if (height > availableHeight * 0.98) {
          height = availableHeight * 0.98;
          width = height * (16 / 9);
        }
      } else {
        width = screenWidth * 0.95;
        height = width / (16 / 9);

        if (height > availableHeight * 0.95) {
          height = availableHeight * 0.95;
          width = height * (16 / 9);
        }
      }
    }

    return {
      width: `${width}px`,
      height: `${height}px`,
      border: "none",
      backgroundColor: "#000",
    };
  };

  const getBottomBarStyle = () => ({
    width: "100%",
    height: "60px",
    backgroundColor: "transparent",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    padding: "0 10px",
    zIndex: 1000,
  });

  const toggleFullScreen = async () => {
    if (!containerRef.current) return;

    try {
      if (getFullscreenElement()) {
        await exitFullscreen();
      } else {
        await requestFullscreen(containerRef.current);
      }
    } catch (error) {
      console.error("Fullscreen toggle failed:", error);
    }
  };

  if (!gameUrl) {
    return <div>Game not found</div>;
  }

  if (isPortableDevice && currentOrientation !== gameOrientation) {
    return <ScreenRotationOverlay isOpen={true} />;
  }

  const handleBackClick = () => {
    trackWebEngageEvent("WebGlGameClosed");
    router.push("/user-home-screen/");
  };

  return (
    <div ref={containerRef} style={getContainerStyle()}>
      <div style={getIframeContainerStyle()}>
        <button
          onClick={handleBackClick}
          style={{
            position: "fixed",
            top: "8px",
            left: "0px",
            zIndex: 1001,
            background: "transparent",
            padding: "4px",
            cursor: "pointer",
            border: "none",
          }}
        >
          <Image src="/images/webGl/userHomeScreen/bckBtn.webp" width={30} height={30} alt="Back" />
        </button>
        <iframe src={gameUrl} style={getIframeStyle()} title={gameName} allowFullScreen />
      </div>

      {!isPortableDevice && (
        <div style={getBottomBarStyle()}>
          <button
            onClick={toggleFullScreen}
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              background: "transparent",
              padding: "5px",
              cursor: "pointer",
              border: "none",
              color: "white",
              fontWeight: "bold",
            }}
          >
            {isFullscreen ? (
              <>
                <span style={{ marginRight: "10px" }}>Normal Screen</span>
                <Image
                  src="/images/webGl/playerScreen/Normalscreen.png"
                  width={30}
                  height={30}
                  alt="Normal screen"
                />
              </>
            ) : (
              <>
                <span style={{ marginRight: "10px" }}>Full Screen</span>
                <Image
                  src="/images/webGl/playerScreen/Fullscreen.png"
                  width={30}
                  height={30}
                  alt="Full screen"
                />
              </>
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default GamePlayer;
