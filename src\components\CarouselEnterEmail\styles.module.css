.carouselWrapper {
  position: relative;
  width: 100%;
  height: 360px;
  overflow: hidden;
}

.backgroundContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.backgroundSlide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  filter: blur(8px);
  transform: scale(1.1);
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.activeBackground {
  opacity: 0.5;
}

.carousel {
  position: relative;
  display: flex;
  transition: transform 0.3s ease-in-out;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.slide {
  position: relative;
  flex: 0 0 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.imageContainer {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.imageContainer img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  max-height: 360px;
}

.active {
  opacity: 1;
}

.indicators {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  border: 1px solid #ffff;
  padding: 6px;
  border-radius: 40px;
  box-sizing: border-box;
  z-index: 50;
}

.indicator {
  width: 8px;
  height: 8px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transition:
    width 0.9s,
    height 0.9s,
    border-radius 0.9s,
    background-color 0.9s;
  margin: 0 5px;
  cursor: pointer;
}

.indicator.active {
  width: 40px;
  height: 8px;
  background-color: #ffff;
  border-radius: 24px;
}

.carouselContentWrapper {
  position: absolute;
  bottom: 60px;
  left: 0;
  width: 100%;
  text-align: center;
  z-index: 2;
}

.carouselContentWrapper h1 {
  font-size: 1rem;
  margin: 0 0 8px 0;
}

.carouselContentWrapper p {
  margin: 0;
  font-size: 0.875rem;
  font-family: var(--font-poppins);
}

@media (max-width: 480px) {
  .carouselContentWrapper {
    bottom: 80px;
  }
}

@media (min-width: 768px) {
  .carouselContentWrapper h1 {
    font-size: 1.85rem !important;
  }

  .carouselContentWrapper p {
    font-size: 0.8rem !important;
  }
}
