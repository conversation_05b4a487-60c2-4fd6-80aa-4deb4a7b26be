import { useTranslations } from "next-intl";
import styles from "./styles.module.css";

export const metadata = {
  title: "SKIDOS Privacy Policy",
};

const PrivacyPage = () => {
  const t = useTranslations("PrivacyPage");

  return (
    <>
      <div className={styles.privacyPage}>
        <div>
          <h1 className={styles.privacyHeading}>{t("title")}</h1>
        </div>

        <div className={styles.privacyContainer}>
          <h2>
            <span>Introduction:</span>
          </h2>
          <p>
            <span>{t("introduction.paragraph1")}</span>
          </p>
          <p className={styles.privacyPargrapDark}>
            <span>{t("introduction.paragraph2")}</span>
          </p>

          <h2 className={styles.privacyApplications}>
            <span>{t("limitedCollection.heading")}</span>
          </h2>
          <p>
            <span>{t("limitedCollection.description")}</span>
          </p>
          <p>
            <span>(i) </span>
            <span className={styles.spanApplicationTitle}>
              {t("limitedCollection.optInAccount.title")}:{" "}
            </span>
            {t("limitedCollection.optInAccount.description")}
          </p>

          <p>
            <span>(ii) </span>
            <span className={styles.spanApplicationTitle}>
              {t("limitedCollection.nonPersonalInfo.title")}{" "}
            </span>
            {t("limitedCollection.nonPersonalInfo.description")}
          </p>

          <p>
            <span>(iii) </span>
            <span className={styles.spanApplicationTitle}>
              {t("limitedCollection.crashReports.title")} –{" "}
            </span>
            {t("limitedCollection.crashReports.description")}
          </p>

          <p>
            <span>(iv) </span>
            <span className={styles.spanApplicationTitle}>
              {t("limitedCollection.installs.title")} –{" "}
            </span>
            {t("limitedCollection.installs.description")}
          </p>

          <h3>
            <span className={styles.notificationHeading}>{t("pushNotifications.title")}</span>
          </h3>
          <p>{t("pushNotifications.description")}</p>

          <p>
            <span className={styles.notificationHeading}>{t("cookies.title")}</span>
          </p>
          <p>{t("cookies.description")}</p>

          <h2>{t("complianceSection.coppaTitle")}</h2>
          <p>{t("complianceSection.coppaDescription1")}</p>
          <p>{t("complianceSection.coppaDescription2")}</p>

          <h2>{t("contact.title")}</h2>
          <p>
            If you have any questions or concerns regarding this Privacy Policy, please send us an
            email at, or raise a ticket at {t("contact.supportLink")}
          </p>

          <h3>Mailing address</h3>
          <p>
            {t("contact.mailingAddress.company")},<br />
            {t("contact.mailingAddress.street")} <br />
            {t("contact.mailingAddress.city")} <br />
            {t("contact.mailingAddress.cvr")}
          </p>
        </div>
      </div>
    </>
  );
};

export default PrivacyPage;
