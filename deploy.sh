#!/bin/bash

# Variables
S3_BUCKET="s3://nextjs-prod-skidos-website"
BUILD_DIR="./out"  # Assuming the 'build' folder is in the same directory as this script
CLOUDFRONT_DISTRIBUTION_ID="EU1QO8T36QU4W"

# Sync ReactJS build to S3 root directory
echo "Uploading to S3 root directory..."
aws s3 sync "$BUILD_DIR" "$S3_BUCKET" --delete

# Invalidate CloudFront cache with correctly formatted paths
echo "Creating CloudFront invalidation..."
aws cloudfront create-invalidation --distribution-id "$CLOUDFRONT_DISTRIBUTION_ID" --paths "/*"

echo "Deployment and invalidation complete."
