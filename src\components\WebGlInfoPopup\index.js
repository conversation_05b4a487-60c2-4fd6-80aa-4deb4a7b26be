"use client";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import styles from "./styles.module.css";

const InfoPopup = ({ isOpen, onClose, data, localeData }) => {
  const t = useTranslations(localeData);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (isOpen) {
      document.body.classList.add(styles.noScroll);
      // Delay to trigger animation
      setTimeout(() => setIsVisible(true), 10);
    } else {
      setIsVisible(false);
      // Wait for animation to complete before removing from DOM
      setTimeout(() => {
        document.body.classList.remove(styles.noScroll);
      }, 400);
    }
    return () => {
      document.body.classList.remove(styles.noScroll);
    };
  }, [isOpen]);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 400);
  };

  if (!isOpen) return null;

  return (
    <div className={styles.overlay}>
      <div className={`${styles.popup} ${isVisible ? styles.popupVisible : styles.popupHidden}`}>
        <img src={data.bannerImage} alt="Parent and child" className={styles.image} />
        <h2>{t(data.heading)}</h2>
        <ul className={styles.list}>
          {data?.popupContent.map((key, index) => (
            <li key={index}>{t(key)}</li>
          ))}
        </ul>
        <button className={styles.okayButton} onClick={handleClose}>
          {t("PopCta")}
        </button>
      </div>
    </div>
  );
};

export default InfoPopup;
