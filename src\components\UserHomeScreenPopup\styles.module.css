.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.noScroll {
  overflow: hidden;
}

.overlayContent {
  background: #fff;
  border-radius: 10px;
  width: 100%;
  border-top-left-radius: 100px;
  border-top-right-radius: 100px;
  position: relative;
  position: absolute;
  bottom: 0;
  box-sizing: border-box;
}

.title {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  margin-top: 0;
  text-align: center;
  color: #007d88;
}

.content {
  text-align: center;
  display: flex;
  gap: 40px;
  justify-content: space-around;
  padding: 0rem 4rem 7rem 4rem;
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none;
  position: relative;
  z-index: 1002;
}

.content::-webkit-scrollbar {
  display: none;
}

.btnHeadingWrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2rem 4rem;
  box-sizing: border-box;
}

.backBtnImg {
  cursor: pointer;
}

.addresebleGamesCard {
  padding: 1rem;
  background-color: #fff;
  border-radius: 32px;
  position: relative;
  box-shadow: 0px 9.01px 0px 0px #000000;
  border: 1px solid #ccc;
}

.addresebleGamesCard p {
  font-family: var(--font-poppins);
  font-weight: 500;
  font-size: 1.3rem;
  margin: 0.5rem 0;
}

.playBtn {
  position: absolute;
  bottom: -35px;
  left: 80px;
}

.bottomImageWrapper {
  padding: 0;
  position: absolute;
  bottom: -10px;
  left: -30px;
  transform: scaleX(-1);
  z-index: 1001;
}

.bottomImageWrapperRight {
  padding: 0;
  position: absolute;
  bottom: -10px;
  right: -30px;
  z-index: 1001;
}

.cardImgs {
  max-width: none;
}

@media screen and (max-height: 500px) and (orientation: landscape) {
  .overlayContent {
    border-top-left-radius: 70px;
    border-top-right-radius: 70px;
  }

  .backBtnImg {
    width: 40px;
    height: 40px;
  }

  .title {
    font-size: 2rem;
  }

  .content {
    gap: 20px;
    padding: 0rem 3rem 3.5rem 3rem;
  }

  .btnHeadingWrapper {
    padding: 1rem 3rem;
  }

  .cardImgs {
    width: 150px;
    height: 150px;
  }

  .addresebleGamesCard {
    padding: 0.7rem;
    background-color: #fff;
    border-radius: 22px;
  }

  .addresebleGamesCard p {
    font-size: 1rem;
    margin: 0.3rem 0;
  }

  .playBtn {
    bottom: -43px;
    left: 70px;
  }

  .popupBottomImgs {
    width: 150px;
    height: 50px;
  }
}

.contentWrapper {
  margin: 0px 4rem 0px 4rem;
}
