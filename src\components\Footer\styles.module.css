.footerWrapper {
  /* max-width: 1512px; */
  max-width: 1728px;
  margin: 0 auto;
}
.pinkFooterWrapper {
  background-image: url("/images/pinkBg.webp");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  width: 100%;
  padding: 8.3rem 0 1.5rem 0;
}

.lockContentWrapper {
  display: flex;
  align-items: center;
  flex-direction: column;
  font-size: 2.5rem;
}

.lockContentWrapper p:nth-child(1) {
  margin: 0;
}

.lockContentWrapper p:nth-child(2) {
  color: #ffff;
  margin: 1rem 0 2rem 0;
}

.carouselBannerBtn {
  margin-top: 0rem;
  background-color: #9258fe;
  font-size: 1.5rem;
  color: #ffff;
  border-radius: 1rem;
  padding: 0.8rem 2rem;
  text-decoration: none;
  border: none;
  box-shadow: 0px 10px 0px rgba(74, 45, 128, 1);
  cursor: pointer;
  font-family: var(--font-poppins);
  font-weight: 500;
}

.gameCardsWrapper {
  display: flex;
  gap: 10px;
  justify-content: center;
  text-align: center;
  margin: 2rem 2rem 0 2rem;
}
.gameCardsFirstSection {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}
.gameCardsFirstSection:nth-child(1) {
  justify-content: flex-end;
  gap: 20px;
}
.gameCardsFirstSection:nth-child(2) {
  justify-content: flex-start;
  gap: 20px;
}
.gameCardCol1 div {
  background-color: #ffff;
  padding: 5px;
  border-radius: 20px;
}
.gameCardCol1 p {
  margin: 0;
  font-size: 0.8rem;
}

.gameCardCol2 {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 15px;
}
.gameCardCol2 div {
  background-color: #ffff;
  padding: 5px;
  border-radius: 20px;
}
.gameCardCol2 p {
  margin: 0;
  font-size: 0.8rem;
}
.gameCardCol1Img {
  object-fit: fill;
}
.footerGreenWrapper {
  position: relative;
  background-image: url("/images/footer.webp");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: top;
  width: 100%;
  min-height: 1250px;
  display: flex;
  align-items: flex-end;
}
.footerContentWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.12);
  position: absolute;
  width: 98%;
  left: 50%;
  transform: translateX(-50%);
  color: #ffff;
  border-radius: 12px;
  flex-direction: column;
  bottom: 10px;
}
.footerContentWrapper div {
  width: 100%;
}
.footerContentRight {
  display: flex;
  flex-direction: column;
  border-left: none;
}
.footerContentRightTop {
  display: flex;
  width: 100% !important;
  text-align: left;
  border-bottom: 0.1px solid rgba(255, 255, 255, 0.3);
  border-top: 0.1px solid rgba(255, 255, 255, 0.3);
  margin-top: 2rem;
  flex-direction: column;
  padding: 1rem;
  box-sizing: border-box;
}
.footerContentRightTop div {
  width: 100%;
  margin-left: 0.3rem;
}
.footerContentRightTop ul {
  list-style: none;
  text-align: left;
  padding: 0;
  margin: 0;
  font-size: 1.2rem;
  font-family: var(--font-poppins);
}
.footerContentRightTop li {
  margin: 0.5rem 0;
  color: rgba(255, 255, 255, 0.7);
}
.linkHoverable:hover {
  display: inline;
  margin: 0.5rem 0;
  color: rgba(255, 255, 255, 1);
  border-bottom: 1px solid rgba(255, 255, 255, 1);
  box-sizing: border-box;
}
.storeIconsWrapper {
  display: grid;
  gap: clamp(8px, 2vw, 20px);
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  max-width: 100%;
  width: 100%;
  align-self: center;
  place-self: center;
  justify-self: center;
  padding: 0 clamp(8px, 2vw, 16px);
  box-sizing: border-box;
}

.storeIconContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: auto;
  transition: transform 0.2s ease-in-out;
}

.storeIconContainer:hover {
  transform: scale(1.05);
}

.storeIconContainer:active {
  transform: scale(0.98);
}

/* Ensure proper touch targets on mobile */
@media (max-width: 767px) {
  .storeIconContainer {
    min-height: 44px; /* Minimum touch target size */
    padding: 8px;
    border-radius: 8px;
  }

  .storeIconContainer:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
  }
}

.footerIconsDiv{
position: absolute;
  bottom: 280px;
  left: 50%;
  transform: translateX(-50%);
}
.footerContentLeft {
  display: flex;
  flex-direction: column;
  gap: 13px;
  padding: 1rem;
  box-sizing: border-box;
  padding: 0;
}
.footerContentLeft div {
  width: 80%;
  margin-left: 0.5rem;
}
.footerContentLeftImg {
  display: flex;
  gap: 5px;
}
.storeIcons {
  cursor: pointer;
  width: 100%;
  height: auto;
  max-width: clamp(120px, 15vw, 200px);
  min-width: 100px;
  object-fit: contain;
  transition: opacity 0.2s ease-in-out;
}

.storeIcons:hover {
  opacity: 0.9;
}

@media (min-width: 768px) {
  .lockContentWrapper {
    font-size: 5rem;
  }
  .carouselBannerBtn {
    font-size: 2rem;
    padding: 0.8rem 3rem;
  }
  .footerGreenWrapper {
    min-height: 1050px;
    /* min-height: clamp(450px, 100vw + 450px, 1050px); */
  }
  .footerContentWrapper {
    flex-direction: row;
  }
  .footerContentRight {
    border-left: 0.1px solid rgba(255, 255, 255, 0.3);
  }
  .footerContentRightTop {
    border-top: none;
    margin-top: 0;
    flex-direction: row;
    padding: 0;
  }
  .footerContentRightTop div {
    width: 33.3%;
    margin-left: 1rem;
  }
  .footerContentLeft {
    gap: 90px;
    padding-left: 2rem;
  }
  .footerContentRightTop div:nth-child(1),
  .footerContentRightTop div:nth-child(2) {
    border-right: 0.1px solid rgba(255, 255, 255, 0.3);
  }
  .footerContentWrapper div:nth-child(1) {
    width: 40%;
  }
  .gameCardCol2 div,
  .gameCardCol1 div {
    padding: 3px;
  }
  .gameCardsFirstSection p {
    font-size: 1rem;
  }
   .footerIconsDiv {
    bottom: 265px;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .storeIconsWrapper {
    grid-template-columns: repeat(4, 1fr);
    max-width: 600px;
    gap: clamp(12px, 2.5vw, 18px);
  }

  .storeIcons {
    max-width: clamp(130px, 18vw, 160px);
  }

  .footerGreenWrapper {
    min-height: 830px !important;
  }
  .footerContentRightTop div:nth-child(1),
  .footerContentRightTop div:nth-child(2) {
    width: 23%;
  }

  .footerIconsDiv {
    bottom: 280px;
  }
}

@media (min-width: 1025px) {
  .storeIconsWrapper {
    grid-template-columns: repeat(4, 1fr);
    max-width: 800px;
    gap: clamp(16px, 2vw, 24px);
  }

  .storeIcons {
    max-width: clamp(160px, 12vw, 200px);
  }
}

@media (min-width: 1550px) {
  .storeIconsWrapper {
    max-width: 900px;
    gap: clamp(20px, 2.5vw, 30px);
  }

  .storeIcons {
    max-width: clamp(180px, 10vw, 220px);
  }

  .footerGreenWrapper {
    min-height: 1200px !important;
  }
}
@media (max-width: 767px) {
  .storeIconsWrapper {
    grid-template-columns: repeat(2, 1fr);
    max-width: 400px;
    gap: clamp(10px, 3vw, 16px);
    padding: 0 clamp(12px, 4vw, 20px);
  }

  .storeIcons {
    max-width: clamp(140px, 35vw, 180px);
    min-width: 120px;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .storeIconsWrapper {
    grid-template-columns: repeat(2, 1fr);
    max-width: 320px;
    gap: clamp(8px, 2.5vw, 12px);
    padding: 0 clamp(8px, 3vw, 16px);
  }

  .storeIcons {
    max-width: clamp(110px, 30vw, 140px);
    min-width: 100px;
  }
}

/* Very small screens */
@media (max-width: 360px) {
  .storeIconsWrapper {
    max-width: 280px;
    gap: clamp(6px, 2vw, 10px);
  }

  .storeIcons {
    max-width: clamp(100px, 28vw, 120px);
    min-width: 90px;
  }
}

@media (max-width: 767px) {
  .footerIconsDiv {
    position: absolute;
    bottom: 740px;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    display: flex;
    justify-content: center;
  }
}


@media screen and (max-height: 500px) and (orientation: landscape) {
  .specificPageFooter {
    display: none !important;
  }
}


