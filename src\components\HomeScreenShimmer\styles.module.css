.profileIconWrapperShimmer {
  display: flex;
  align-items: center;
  gap: 10px;
}
.profileIconWrapperShimmer div:nth-child(1) {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  animation-duration: 2.2s;
  animation-fill-mode: forwards;
  animation-iteration-count: infinite;
  animation-name: shimmer;
  animation-timing-function: linear;
  background: #ddd;
  background: linear-gradient(to right, #bcbaba 8%, #f0f0f0 18%, #bcbaba 33%);
  background-size: 1200px 100%;
}
.profileIconWrapperShimmer div:nth-child(2) {
  width: 160px;
  height: 40px;
  border-radius: 20px;
  animation-duration: 2.2s;
  animation-fill-mode: forwards;
  animation-iteration-count: infinite;
  animation-name: shimmer;
  animation-timing-function: linear;
  background: #ddd;
  background: linear-gradient(to right, #bcbaba 8%, #f0f0f0 18%, #bcbaba 33%);
  background-size: 1200px 100%;
}

.gameThemeCardShimmer {
  width: 290px;
  height: 200px;
  transform: skew(10deg);
  position: relative;
  border-radius: 14px;
  overflow: hidden;
  flex-shrink: 0;
  box-sizing: border-box;
  animation-duration: 2.2s;
  animation-fill-mode: forwards;
  animation-iteration-count: infinite;
  animation-name: shimmer;
  animation-timing-function: linear;
  background: #ddd;
  background: linear-gradient(to right, #bcbaba 8%, #f0f0f0 18%, #bcbaba 33%);
  background-size: 1200px 100%;
}
@keyframes shimmer {
  0% {
    background-position: -1200px 0;
  }
  100% {
    background-position: 1200px 0;
  }
}

@media screen and (max-height: 500px) and (orientation: landscape) {
  .gameThemeCardShimmer {
    width: 155px;
    height: 105px;
  }
}
