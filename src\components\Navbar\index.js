"use client";
import { useAuth } from "@/context/AuthContext";
import { trackWebEngageEvent } from "@/utils/webengage";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";
import Cross from "../../../public/images/cross.png";
import skidosLogo from "../../../public/images/skidosLogo.png";
import LanguageSelector from "../LanguageSelector";
import styles from "./styles.module.css";

const NavbarHome = () => {
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);
  const t = useTranslations("NavbarHome");
  const getLinkClass = (pathnames) => {
    return pathname === pathnames ? styles.activeLink : "";
  };
  const specificPages = ["/user-home-screen/", "/game-player/"];
  const isSpecificPage = specificPages.includes(pathname);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      document.body.classList.add(styles.noScroll);
    } else {
      document.body.classList.remove(styles.noScroll);
    }
  };
  const { isLoggedIn, isSubscribed } = useAuth();

  const excludedPaths = [
    "/",
    // "/login/",
    "/profile/",
    // "/login-otp/",
    "/about-us/",
    "/products/",
    "/blogs/",
    "/news/",
    "/terms/",
    "/privacy-policy/",
    "/partnership/",
    // "/user-home-screen/",
    // "/acquisition/",
    // "/purchase/",
    "/career/",
    "/acquisition_success/",
    // "/get-started/"
  ];

  const showLanguageSelector = true;

  return (
    <nav
      className={`${styles.navbar} ${isSpecificPage ? styles.specificPageNavbar : ""}`}
      id="navbar"
    >
      <div className={styles.logo}>
        <Link href="/">
          <Image
            src={skidosLogo}
            alt={t("LogoAlt")}
            width={167}
            height={52}
            className={styles.logoImg}
          />
        </Link>
      </div>
      <div className={`${styles.menuIcon} ${isOpen ? styles.change : ""}`} onClick={toggleMenu}>
        <div className={styles.bar}></div>
        <div className={styles.bar}></div>
        <div className={styles.bar}></div>
      </div>
      <ul className={`${styles.navMenu} ${isOpen ? styles.active : ""}`}>
        <li className={styles.navItem}>
          <Image
            src={Cross}
            width={41}
            height={41}
            className={styles.crossImg}
            onClick={toggleMenu}
            alt={t("CloseAlt")}
          />
        </li>
        <li className={styles.navItem}>
          <Link href="/" className={`${styles.navLink} ${getLinkClass("/")}`} onClick={toggleMenu}>
            {t("Home")}
          </Link>
        </li>
        <li className={styles.navItem}>
          <Link
            href="/about-us"
            className={`${styles.navLink} ${getLinkClass("/about-us/")}`}
            onClick={toggleMenu}
          >
            {t("AboutUs")}
          </Link>
        </li>
        <li className={styles.navItem}>
          <Link
            href="/products"
            className={`${styles.navLink} ${getLinkClass("/products/")}`}
            onClick={toggleMenu}
          >
            {t("Products")}
          </Link>
        </li>
        <li className={styles.navItem}>
          {isLoggedIn ? (
            <Link
              href="/profile"
              className={`${styles.navLink} ${getLinkClass("/profile/")}`}
              onClick={toggleMenu}
            >
              {t("Profile")}
            </Link>
          ) : (
            <Link
              href="/login"
              className={`${styles.navLink} ${getLinkClass("/login/")}`}
              onClick={toggleMenu}
            >
              {t("Login")}
            </Link>
          )}
        </li>
        {!(isLoggedIn && isSubscribed) && (
          <li className={styles.navItem}>
            <Link
              href="/get-started"
              className={`${styles.navLink} ${styles.freeTrailBtn}`}
              onClick={() => {
                toggleMenu();
                trackWebEngageEvent("WebGLFreeTrialClk");
              }}
            >
              {t("FreeTrial")}
            </Link>
          </li>
        )}
        {showLanguageSelector && (
          <li>
            <LanguageSelector />
          </li>
        )}
      </ul>
    </nav>
  );
};

export default NavbarHome;
