"use client";
import Image from "next/image";
import PropTypes from "prop-types";
import styles from "./styles.module.css"; // Make sure styles exist or rename accordingly

const StoreIcons = ({ icons, styleClass }) => {
  return (
    <div className={`${styles.storeIconsWrapper} ${styleClass}`}>
      {icons.map((icon, index) => (
        // eslint-disable-next-line jsx-a11y/click-events-have-key-events
        <div
          key={index}
          onClick={icon.onClick}
          role="button"
          tabIndex={0}
          className={styles.storeIconContainer}
        >
          <Image
            src={icon.src}
            width={198}
            height={60}
            alt={icon.alt}
            className={styles.storeIcons}
            sizes="(max-width: 480px) 45vw, (max-width: 768px) 40vw, (max-width: 1024px) 20vw, 15vw"
            priority={false}
          />
        </div>
      ))}
    </div>
  );
};

StoreIcons.propTypes = {
  icons: PropTypes.arrayOf(
    PropTypes.shape({
      src: PropTypes.string.isRequired,
      alt: PropTypes.string.isRequired,
      onClick: PropTypes.func.isRequired,
    })
  ).isRequired,
};

export default StoreIcons;
