"use client";
import FormCtaButton from "@/components/FormCtaButton";
import { inforPopupData } from "@/constants";
import { usePlayerContext } from "@/context/CreatePlayerContext";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useState } from "react";
import InfoPopup from "../WebGlInfoPopup";
import styles from "./styles.module.css";

const avatarsInfo = [
  {
    name: "<PERSON>",
    imagePath: "/images/webGl/avatar/sara_f_17.webp",
    index: 17,
    gender: "female",
  },
  {
    name: "<PERSON>",
    imagePath: "/images/webGl/avatar/alex_m_12.webp",
    index: 12,
    gender: "male",
  },
  {
    name: "<PERSON>",
    imagePath: "/images/webGl/avatar/alex_m_18.webp",
    index: 18,
    gender: "male",
  },
  {
    name: "<PERSON>",
    imagePath: "/images/webGl/avatar/alex_m_6.webp",
    index: 6,
    gender: "male",
  },
  {
    name: "<PERSON>",
    imagePath: "/images/webGl/avatar/christina_f_4.webp",
    index: 4,
    gender: "female",
  },
  {
    name: "Deepti",
    imagePath: "/images/webGl/avatar/deepti_f_7.webp",
    index: 7,
    gender: "female",
  },
  {
    name: "Faizan",
    imagePath: "/images/webGl/avatar/faizan_m_11.webp",
    index: 11,
    gender: "male",
  },
  {
    name: "Gary",
    imagePath: "/images/webGl/avatar/gary_m_2.webp",
    index: 2,
    gender: "male",
  },
  {
    name: "Hanna",
    imagePath: "/images/webGl/avatar/hanna_f_16.webp",
    index: 16,
    gender: "female",
  },
  {
    name: "Hanna",
    imagePath: "/images/webGl/avatar/hanna_f_19.webp",
    index: 19,
    gender: "female",
  },
  {
    name: "Hanna",
    imagePath: "/images/webGl/avatar/hanna_f_9.webp",
    index: 9,
    gender: "female",
  },
  {
    name: "Jim",
    imagePath: "/images/webGl/avatar/jim_m_10.webp",
    index: 10,
    gender: "male",
  },
  {
    name: "Lee",
    imagePath: "/images/webGl/avatar/lee_m_15.webp",
    index: 15,
    gender: "male",
  },
  {
    name: "Lee",
    imagePath: "/images/webGl/avatar/lee_m_8.webp",
    index: 8,
    gender: "male",
  },
  {
    name: "Mary",
    imagePath: "/images/webGl/avatar/mary_f_3.webp",
    index: 3,
    gender: "female",
  },
  {
    name: "Niki",
    imagePath: "/images/webGl/avatar/niki_f_13.webp",
    index: 13,
    gender: "female",
  },
  {
    name: "Pankaj",
    imagePath: "/images/webGl/avatar/pankaj_m_5.webp",
    index: 5,
    gender: "male",
  },
  {
    name: "Sakura",
    imagePath: "/images/webGl/avatar/sakura_f_0.webp",
    index: 0,
    gender: "female",
  },
  {
    name: "Salma",
    imagePath: "/images/webGl/avatar/salma_f_14.webp",
    index: 14,
    gender: "female",
  },
  {
    name: "Sam",
    imagePath: "/images/webGl/avatar/sam_m_1.webp",
    index: 1,
    gender: "male",
  },
];

const CreatePlayerSection = ({ onSubmit }) => {
  const { playerData, updatePlayerData } = usePlayerContext();
  const isReturningUser = playerData.name && playerData.selectedAge && playerData.selectedAvatar;
  const [name, setName] = useState(playerData.name || "");
  const [isNameSubmitted, setIsNameSubmitted] = useState(false);
  const [isNameFocused, setIsNameFocused] = useState(false);
  const [isAvatarSubmitted, setAvatarSubmitted] = useState(false);
  const [selectedAge, setSelectedAge] = useState(playerData.selectedAge || null);
  const [selectedAvatar, setSelectedAvatar] = useState(playerData.selectedAvatar || "");
  const [errors, setErrors] = useState({
    nameError: "",
    ageError: "",
    avatarSelectedError: "",
  });
  const [showPopup, setShowPopup] = useState(false);
  const t = useTranslations("CreatePlayer");

  const [currentSection, setCurrentSection] = useState("name");

  const handleNameInput = (e) => {
    const value = e.target.value;
    const validatedValue = value.replace(/[^a-zA-Z\s]/g, "").slice(0, 10);
    setName(validatedValue);

    if (validatedValue.trim().length >= 1) {
      setErrors((prevErrors) => ({ ...prevErrors, nameError: "" }));
      setIsNameSubmitted(true);
      setCurrentSection("avatar");
    } else {
      setErrors((prevErrors) => ({
        ...prevErrors,
        nameError: t("ErrorMsgAll"),
      }));
      setIsNameSubmitted(false);
      setCurrentSection("name"); // Keep in name section if invalid
    }
  };

  const handleNameFocus = () => {
    setIsNameFocused(true);
    setCurrentSection("name");
  };

  const handleNameBlur = (e) => {
    setIsNameFocused(false);
    handleNameInput(e);
  };

  const handleAvatarSelect = (avatar) => {
    setSelectedAvatar(avatar);
    setErrors((prevErrors) => ({ ...prevErrors, avatarSelectedError: "" }));
    setAvatarSubmitted(true);
    setCurrentSection("age"); // Move to age section after avatar selection
  };

  const handleAgeClick = (age) => {
    setSelectedAge(age);
    setErrors((prevErrors) => ({ ...prevErrors, ageError: "" }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    let hasErrors = false;
    const newErrors = {
      nameError: "",
      avatarSelectedError: "",
      ageError: "",
    };

    if (name.trim().length < 1) {
      newErrors.nameError = t("ErrorMsgAll");
      hasErrors = true;
    }
    if (!selectedAvatar) {
      newErrors.avatarSelectedError = t("ErrorMsgAll");
      hasErrors = true;
    }
    if (!selectedAge) {
      newErrors.ageError = t("ErrorMsgAll");
      hasErrors = true;
    }

    setErrors(newErrors);

    if (!hasErrors) {
      updatePlayerData({ name, selectedAge, selectedAvatar });
      onSubmit();
    }
  };

  const borderClass =
    errors.nameError && currentSection === "name"
      ? styles.redBorder
      : isNameFocused && name.trim().length >= 1
        ? styles.greenBorder
        : styles.defaultBorder;

  const avatarWrapperClass = `${styles.avatarInputWrapper} 
  ${isNameSubmitted && !isReturningUser ? styles.avatarVisible : ""} 
  ${isAvatarSubmitted && !isReturningUser ? styles.showNoBorder : ""} 
  ${!isReturningUser ? styles.disabledSections : ""}
  ${errors.avatarSelectedError && currentSection === "avatar" ? styles.redBorder : ""}`;

  const ageWrapperClass = `${styles.ageInputWrapper} 
  ${isAvatarSubmitted ? styles.avatarVisible : ""} 
  ${!isReturningUser ? styles.disabledSections : ""}
  ${errors.ageError && currentSection === "age" ? styles.redBorder : ""}`;

  let ageWrapperSelectedStyle = {};
  if (selectedAge) {
    ageWrapperSelectedStyle = { border: "none", boxShadow: "none" };
  }

  return (
    <div className={styles.createPlayerWrapper}>
      <header className={styles.createPlayerHeader}>
        <h1>{t("Heading")}</h1>
        <p>{t("SubHeading")}</p>
      </header>
      <form onSubmit={handleSubmit}>
        <div
          className={`${styles.nameInputWrapper} ${!isNameSubmitted && !isReturningUser && styles.avatarVisible}`}
        >
          <label
            htmlFor="name"
            style={{ color: errors.nameError && currentSection === "name" ? "red" : "" }}
          >
            {t("NameLabel")}
          </label>
          <input
            id="name"
            type="text"
            placeholder={t("NameInputPlaceholder")}
            onChange={handleNameInput}
            onFocus={handleNameFocus}
            onBlur={handleNameBlur}
            value={name}
            className={borderClass}
          />
        </div>

        <div className={avatarWrapperClass}>
          <label
            htmlFor="avatar"
            style={{
              color: errors.avatarSelectedError && currentSection === "avatar" ? "red" : "",
            }}
          >
            {t("AvatarLabel")}
          </label>
          <div className={styles.avatarWrapper}>
            <div className={styles.selectedAvatar}>
              <Image
                className={styles.selectedAvatarImg}
                src={
                  !selectedAvatar.imagePath
                    ? "/images/webGl/avatar/emptyAvatar.webp"
                    : selectedAvatar.imagePath
                }
                width={125}
                height={125}
                alt="Selected Avatar"
              />
              <Image
                className={styles.platformImg}
                src="/images/webGl/avatar/Platform.webp"
                width={106}
                height={30}
                alt="Platform"
              />
            </div>
            <div className={styles.avatarsList}>
              <div className={styles.avatarsListRow1}>
                {avatarsInfo.slice(0, 10).map((avatar) => (
                  <div
                    style={{ display: "flex", justifyContent: "center", alignItems: "center" }}
                    key={avatar.index}
                    onClick={() => handleAvatarSelect(avatar)}
                    className={`${styles.avatarItem} ${
                      selectedAvatar.imagePath === avatar.imagePath ? styles.avatarItemSelected : ""
                    }`}
                  >
                    <Image src={avatar.imagePath} width={62} height={62} alt={avatar.name} />
                  </div>
                ))}
              </div>
              <div className={styles.avatarsListRow2}>
                {avatarsInfo.slice(10, 20).map((avatar) => (
                  <div
                    style={{ display: "flex", justifyContent: "center", alignItems: "center" }}
                    key={avatar.index}
                    onClick={() => handleAvatarSelect(avatar)}
                    className={`${styles.avatarItem} ${
                      selectedAvatar.imagePath === avatar.imagePath ? styles.avatarItemSelected : ""
                    }`}
                  >
                    <Image src={avatar.imagePath} width={62} height={62} alt={avatar.name} />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className={ageWrapperClass} style={{ ...ageWrapperSelectedStyle }}>
          <label
            htmlFor="age"
            style={{ color: errors.ageError && currentSection === "age" ? "red" : "" }}
          >
            {t("AgeLabel")}
          </label>
          <div className={styles.ageWrapper}>
            <div className={styles.ageWrapperRow1}>
              {[2, 3, 4, 5, 6].map((age) => (
                <div
                  key={age}
                  onClick={() => handleAgeClick(age)}
                  className={`${styles.ageBox} ${selectedAge === age ? styles.selectedAge : ""}`}
                >
                  {age}
                </div>
              ))}
            </div>
            <div className={styles.ageWrapperRow2}>
              {[7, 8, 9, 10].map((age) => (
                <div
                  key={age}
                  onClick={() => handleAgeClick(age)}
                  className={`${styles.ageBox} ${selectedAge === age ? styles.selectedAge : ""}`}
                >
                  {age}
                </div>
              ))}
            </div>
          </div>
        </div>

        {(errors.nameError || errors.ageError || errors.avatarSelectedError) && (
          <div className={styles.errorWrapper}>
            <p>
              <Image src="/images/webGl/warning.png" height={15} width={15} alt="Warning" />{" "}
              {t("ErrorMsgAll")}
            </p>
          </div>
        )}
        <FormCtaButton text={t("CtaBtn")} />
      </form>
      <p className={styles.infoText} onClick={() => setShowPopup((prev) => !prev)}>
        <Image src="/images/webGl/normalInfo.png" height={15} width={15} alt="Info" />{" "}
        {t("InfoText")}
      </p>
      {showPopup && (
        <InfoPopup
          isOpen={showPopup}
          onClose={() => setShowPopup((prev) => !prev)}
          data={inforPopupData[0]}
          localeData="CreatePlayer"
        />
      )}
    </div>
  );
};

export default CreatePlayerSection;
