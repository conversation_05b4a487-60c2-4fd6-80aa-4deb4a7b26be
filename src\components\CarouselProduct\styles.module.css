.carouselProductBackgroud,
.carouselProductBackgroud2,
.carouselProductBackgroud3,
.carouselProductBackgroud4,
.carouselProductBackgroud5 {
  max-width: 100%;
  background-image: url("/images/product/math.webp");
  background-color: #e792cc;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  object-fit: cover;
}
.carouselProductBackgroud2 {
  background-image: url("/images/product/avatar.webp");
  background-color: #01b0fa;
}
.carouselProductBackgroud3 {
  background-image: url("/images/product/iris.webp");
  background-color: #02c7a5;
}
.carouselProductBackgroud4 {
  background-image: url("/images/product/sel.webp");
  background-color: #b0f1fb;
}
.carouselProductBackgroud5 {
  background-image: url("/images/product/tracing.webp");
  background-color: #02c7a5;
}
.carouselProductBackgroud div,
.carouselProductBackgroud2 div,
.carouselProductBackgroud3 div,
.carouselProductBackgroud4 div,
.carouselProductBackgroud5 div {
  width: 50%;
  color: #ffff;
}
.productTextWrapper {
  display: flex;
  justify-content: center;
  flex-direction: column;
  padding: 0 4rem;
  flex-wrap: wrap;
  box-sizing: border-box;
}
.productTextWrapper h2 {
  font-size: 5rem;
  margin: 0;
}
.productTextWrapper p {
  font-size: 1.5rem;
  font-family: var(--font-poppins);
}
.productBannerImg {
  width: 100%;
  height: 100%;
}
.carouselBannerBtn {
  width: 70%;
  margin-top: 2rem;
  background-color: #9258fe;
  font-size: 2.2rem;
  color: #ffff;
  border-radius: 1rem;
  padding: 0.8rem 2rem;
  text-decoration: none;
  border: none;
  box-shadow: 0px 10px 0px rgba(74, 45, 128, 1);
  cursor: pointer;
  margin-right: 5rem;
  font-family: var(--font-poppins);
  font-weight: 500;
}

@media (min-width: 768px) and (max-width: 1024px) {
  .productTextWrapper {
    padding: 0 1rem !important;
  }
}

@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) and (orientation: portrait) {
  .productTextWrapper h2 {
    font-size: 3rem;
  }
  .productTextWrapper p {
    font-size: 1.1rem;
  }
  .carouselBannerBtn {
    margin-top: 1rem;
    font-size: 1.5rem;
    border-radius: 1.3rem;
    margin-right: 5rem;
  }
}

/*Mobile CSS************************************************************************************************** */

.carouselProductBackgroudMb,
.carouselProductBackgroudMb2,
.carouselProductBackgroudMb3,
.carouselProductBackgroudMb4,
.carouselProductBackgroudMb5 {
  width: 100%;
  height: 600px;
  background-image: url("/images/product/mobile_math.webp");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  object-fit: contain;
}
.carouselProductBackgroudMb2 {
  background-image: url("/images/product/mobile_avatar.webp");
}
.carouselProductBackgroudMb3 {
  background-image: url("/images/product/mobile_iris.webp");
}
.carouselProductBackgroudMb4 {
  background-image: url("/images/product/mobile_sel.webp");
}
.carouselProductBackgroudMb5 {
  background-image: url("/images/product/mobile.webp");
}
.productTextWrapperMb {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
  margin: 0 auto;
}
.productTextWrapperMb h2 {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}
.productTextWrapperMb p {
  margin: 0.5rem;
  font-size: 1.2rem;
  text-align: center;
  font-family: var(--font-poppins);
}
.carouselBannerBtnMb {
  font-family: var(--font-poppins);
  font-weight: 500;
  margin-top: 1.5rem;
  background-color: #9258fe;
  font-size: 1.5rem;
  color: #ffff;
  border-radius: 1rem;
  padding: 0.8rem 2rem;
  text-decoration: none;
  border: none;
  box-shadow: 0px 10px 6px rgba(0, 0, 0, 0.978);
  cursor: pointer;
}
