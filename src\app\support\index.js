"use client";
import Accordion from "@/components/Accordian";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useRef, useState } from "react";
import { RiAttachmentLine } from "react-icons/ri";
import Mascots from "../../../public/images/contactUs/contactUsMascots.webp";
import styles from "./styles.module.css";
// import { LiaExternalLinkSquareAltSolid } from "react-icons/lia";

const Support = () => {
  const t = useTranslations("Support");
  const fileInputRef = useRef(null);
  const [fileName, setFileName] = useState(t("form.attachment"));

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      setFileName(file.name);
    }
  };

  const handleIconClick = () => {
    fileInputRef.current.click();
  };

  return (
    <div className={styles.supportWrapper}>
      <header className={styles.contactUsHeader}>
        <div>
          <h2>{t("header.title")}</h2>
          <div className={styles.addressWrapper}>
            <p>{t("header.addressLabel")}</p>
            <p>{t("header.address")}</p>
          </div>
          <div className={styles.addressWrapper}>
            <p>{t("header.emailLabel")}</p>
            <p>{t("header.email")}</p>
          </div>
        </div>
        <div>
          <Image alt={t("header.mascotAlt")} src={Mascots} className={styles.contactUsMascotsImg} />
        </div>
      </header>

      <div className={styles.accordionWrapper}>
        <h2>{t("faq.title")}</h2>
        <Accordion />
      </div>

      <section className={styles.contactUsContainer}>
        <h1>{t("form.title")}</h1>
        <form className={styles.contactUsForm}>
          <div className={styles.nameEmailWrapper}>
            <div className={styles.formGroup}>
              <input
                placeholder={t("form.emailPlaceholder")}
                type="email"
                id="email"
                name="email"
              />
            </div>
          </div>
          <div className={`${styles.formGroup} ${styles.fullWidth}`}>
            <textarea
              placeholder={t("form.messagePlaceholder")}
              id="message"
              name="message"
              rows="10"
            ></textarea>
          </div>
          <div className={styles.fileUploadContainer}>
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              className={styles.fileInput}
            />
            <RiAttachmentLine className={styles.uploadButton} onClick={handleIconClick} />
            {fileName && <div className={styles.fileName}>{fileName}</div>}
          </div>
          <button type="submit" className={styles.formBtn}>
            {t("form.submitButton")}
          </button>
        </form>
      </section>

      <section className={styles.linksWrapper}>
        <h1>{t("links.title")}</h1>
        <div className={styles.linksContent}>
          <p>{t("links.termsOfService")}</p>
          {/* <LiaExternalLinkSquareAltSolid className={styles.linkIcon} /> */}
        </div>
        <div className={styles.linksContent}>
          <p>{t("links.termsOfService")}</p>
          {/* <LiaExternalLinkSquareAltSolid className={styles.linkIcon} /> */}
        </div>
        <div className={styles.linksContent}>
          <p>{t("links.termsOfService")}</p>
          {/* <LiaExternalLinkSquareAltSolid className={styles.linkIcon} /> */}
        </div>
      </section>
    </div>
  );
};

export default Support;
