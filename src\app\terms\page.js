import { useTranslations } from "next-intl";
import styles from "./styles.module.css";

export const metadata = {
  title: "SKIDOS Terms | Terms of Service",
};

const TeamsPage = () => {
  const t = useTranslations("terms");

  return (
    <>
      <div className={styles.teamContainer}>
        <div className={styles.teamHeading}>
          <h1>{t("pageTitle")}</h1>
        </div>
        <div className={styles.teamParagrap}>
          <p>{t("lastUpdated")}</p>
          <p>{t("ageWarning")}</p>
          <p>{t("accessAcknowledge")}</p>
          <p>{t("contact")}</p>
          <p>
            {t("exitWarning")}
            <a href="https://skidos.com/"> www.skidos.com</a>&nbsp;
          </p>

          <p>
            <strong>{t("sections.acknowledgment.title")}</strong>
          </p>
          <p>{t("sections.acknowledgment.content")}</p>

          <p>
            <strong>{t("sections.userInfo.title")}</strong>
          </p>
          <p>{t("sections.userInfo.content")}</p>

          <p>
            <strong>{t("sections.userAccount.title")}</strong>
          </p>
          <p>{t("sections.userAccount.content")}</p>

          <p>
            <strong>{t("sections.conduct.title")}</strong>
          </p>
          <p>{t("sections.conduct.content")}</p>

          <p>
            <strong>{t("sections.intellectualProperty.title")}</strong>
          </p>
          <p>{t("sections.intellectualProperty.content")}</p>

          <p>
            <strong>{t("sections.charges.title")}</strong>
          </p>
          <p>{t("sections.charges.content")}</p>

          <p>
            <strong>{t("refundPolicy.title")}</strong>
          </p>
          <p>{t("refundPolicy.applicability")}</p>
          <ul>
            <li>{t("refundPolicy.points.autoRenewal")}</li>
            <li>{t("refundPolicy.points.refundRequest")}</li>
            <li>{t("refundPolicy.points.discretion")}</li>
            <li>{t("refundPolicy.points.eligibility")}</li>
            <li>{t("refundPolicy.points.refundMethod")}</li>
          </ul>

          <p>{t("effectiveDate")}</p>
        </div>
      </div>
    </>
  );
};

export default TeamsPage;
