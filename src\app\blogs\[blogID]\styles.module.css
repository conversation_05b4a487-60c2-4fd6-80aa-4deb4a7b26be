.blogDeatilWrapper {
  /* max-width: 1512px; */
  max-width: 1728px;
  margin: 0rem auto;
  padding: 0.5rem;
  font-family: var(--font-poppins);
}
.blogDeatilWrapper h1 {
  margin: 0;
}
.imageContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  background-color: #f9f9f9;
  margin-bottom: 2rem;
}
.blogImage {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}
.headAttributes {
  display: flex;
  gap: 1px;
  margin: 0;
  font-size: 0.9rem;
  justify-content: space-between;
}

@media (min-width: 768px) {
  .blogDeatilWrapper {
    padding: 3rem;
  }
  .headAttributes {
    font-size: 1rem;
    gap: 5px;
    justify-content: left;
  }
}
