{"name": "next-skidos-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "eslint --fix .", "prettier": "prettier --write \"**/*.{js,jsx,json,css,scss}\"", "prettier:check": "prettier --check \"**/*.{js,jsx,json,css,scss}\"", "format": "npm run prettier && npm run lint:fix", "postbuild": "next-sitemap", "translate": "node src/scripts/generateTranslations.js"}, "dependencies": {"@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@stripe/stripe-js": "^4.4.0", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "axios": "^1.7.9", "bootstrap": "^5.3.7", "gsap": "^3.12.5", "hls.js": "^1.6.5", "html-react-parser": "^5.1.12", "js-cookie": "^3.0.5", "next": "14.2.3", "next-intl": "^3.23.5", "next-sitemap": "^4.2.3", "plyr-react": "^5.3.0", "react": "^18", "react-cookie-consent": "^9.0.0", "react-dom": "^18", "react-ga4": "^2.1.0", "react-loading-skeleton": "^3.4.0", "react-responsive-carousel": "^3.2.23", "react-toastify": "^10.0.6", "sharp": "^0.33.5", "swiper": "^11.1.4"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.2.3", "eslint-config-prettier": "^10.0.1", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "prettier": "^3.4.2"}}