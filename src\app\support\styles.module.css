.supportWrapper {
  /* max-width: 1512px; */
  max-width: 1728px;
  margin: 0 auto;
}

.contactUsHeader {
  max-width: 100%;
  background-image: url("/images/contactUs/contactUsBanner.webp");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  object-fit: cover;
  flex-direction: column;
}
.contactUsHeader > div {
  display: flex;
  width: 95%;
  justify-content: center;
  flex-direction: column;
}
.contactUsHeader > div:nth-child(1) {
  margin: 0 auto;
}
.contactUsHeader > div:nth-child(2) {
  align-items: center;
}
.contactUsHeader h2 {
  font-size: 3rem;
}
.addressWrapper {
  display: flex;
  flex-direction: column;
  font-family: var(--font-poppins);
}
.addressWrapper p:nth-child(1) {
  font-weight: 700;
  font-size: 1.5rem;
  margin: 1rem 0 -0.8rem 0;
}
.contactUsMascotsImg {
  max-width: 90%;
  height: auto;
  object-fit: contain;
}

.accordionWrapper {
  margin: 1rem;
}
.accordionWrapper h2 {
  font-size: 2.5rem;
}

.contactUsContainer {
  background-color: #f9f9f9;
  padding: 1rem;
  width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
}
.contactUsContainer h1 {
  font-size: 2.5rem;
}
.nameEmailWrapper {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.formGroup input {
  height: 52px;
  border: none;
  background-color: transparent;
  border-bottom: 1px solid black;
  padding: 0 1rem;
  font-size: 1.3rem;
  color: #5b5b5b;
  width: 100%;
  margin: 1rem 0;
}
.formGroup textarea {
  background-color: transparent;
  border: 1px solid black;
  padding: 1rem 1rem;
  font-size: 1.3rem;
  color: #5b5b5b;
  width: 100%;
  margin: 2rem 0;
  border-radius: 8px;
  box-sizing: border-box;
}
.formGroup input:focus,
.formGroup textarea:focus {
  outline: none;
}
.formGroup input::placeholder,
.formGroup textarea::placeholder {
  font-size: 1.3rem;
  color: #5b5b5b;
}

.nameEmailWrapper div {
  width: 100%;
}
.nameEmailWrapper input {
  width: 100%;
  box-sizing: border-box;
}

.formBtn {
  margin-top: 2rem;
  background-color: #9258fe;
  font-size: 1.2rem;
  color: #ffff;
  border-radius: 0.8rem;
  padding: 0.8rem 1.5rem;
  text-decoration: none;
  border: none;
  box-shadow:
    inset 0px 5px 5px rgba(203, 177, 252, 0.9),
    0px 5px 6px rgba(0, 0, 0, 0.978);
  cursor: pointer;
  margin-right: 5rem;
}

.fileUploadContainer {
  display: flex;
  align-items: center;
  font-size: 1rem;
}

.fileInput {
  display: none;
}

.uploadButton {
  font-size: 2rem;
  cursor: pointer;
}
.linksWrapper {
  padding: 1rem;
}
.linksWrapper h1 {
  font-size: 2.5rem;
}

.linksContent {
  font-size: 1.2rem;
  font-family: var(--font-poppins);
  color: #0169dd;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 2px solid #dbdbdb;
  cursor: pointer;
}
.linkIcon {
  font-size: 2rem;
}

@media (min-width: 768px) {
  .contactUsHeader {
    flex-direction: row;
  }
  .contactUsHeader > div {
    width: 50%;
  }
  .contactUsHeader > div:nth-child(1) {
    padding: 0 3rem;
    margin: 0 auto;
  }
  .accordionWrapper {
    margin: 3rem;
  }
  .linksWrapper,
  .contactUsContainer {
    padding: 3rem;
  }
}
@media (min-width: 768px) and (max-width: 1024px) {
  .contactUsHeader > div:nth-child(1) {
    padding: 0 2rem;
    margin: 0 auto;
  }
}
