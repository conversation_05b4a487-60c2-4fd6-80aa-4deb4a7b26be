import BlogsClient from "./BlogsClient";

export const metadata = {
  title: "SKIDOS Blog | Tips & Insights on Fun Learning for Kids",
  description:
    "Read the SKIDOS Blog for expert tips, insights, and resources on fun and effective learning for kids. Explore how to make education enjoyable with our games and activities.",
};

async function fetchPosts(page = 1) {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL}/wp-json/wp/v2/posts?per_page=10&page=${page}&after=2020-01-01T00:00:00`,
    {
      cache: "no-store", // This makes the fetch happen at runtime
    }
  );
  if (!response.ok) {
    throw new Error("Failed to fetch posts");
  }
  return response.json();
}

export default async function BlogsPage() {
  const initialPosts = await fetchPosts(1);

  return <BlogsClient initialPosts={initialPosts} />;
}
