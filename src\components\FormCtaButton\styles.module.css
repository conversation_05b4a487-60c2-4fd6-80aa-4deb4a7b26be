.formCtaButton {
  justify-items: center;
  margin-top: 1rem;
  background-color: #9258fe;
  font-size: 2rem;
  color: #ffff;
  border-radius: 1rem;
  padding: 0.5rem 6.5rem;
  text-decoration: none;
  border: none;
  box-shadow: 0px 10px 0px rgba(74, 45, 128, 1);
  cursor: pointer;
  width: auto;
  font-family: var(--font-nevermind-medium);
}

.loader {
  display: flex;
  justify-content: center;
  align-items: center;
  justify-self: center;
  place-self: center;
  align-self: center;
  align-content: center;
  align-self: center;
  border: 6px solid #f3f3f3;
  border-top: 6px solid #9258fe;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
}

.loaderContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  align-self: center;
  align-content: center;
  width: 100%;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@media only screen and (max-width: 480px) {
  .formCtaButton {
    font-size: 1.5rem;
    /* padding: 0.5rem 6.5rem; */
  }
}
