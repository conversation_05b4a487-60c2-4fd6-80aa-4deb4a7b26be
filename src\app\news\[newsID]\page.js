import { useTranslations } from "next-intl";
import ArticleDetails from "./ArticleDetails";

export async function generateMetadata({ params }) {
  const { newsID } = params;

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL}/wp-json/wp/v2/news?slug=${newsID}`,
    {
      next: { revalidate: 3600 }, // Revalidate every hour
    }
  );

  if (!response.ok) {
    return {
      title: "Article Not Found | SKIDOS News",
      description: "The requested news article could not be found.",
    };
  }

  const postData = await response.json();
  const post = postData[0];
  const yoast = post?.yoast_head_json;

  return {
    title: yoast?.title || post?.title?.rendered,
    description: yoast?.description || post?.excerpt?.rendered?.slice(0, 160),
    openGraph: {
      title: yoast?.og_title,
      description: yoast?.og_description,
      type: "article",
      publishedTime: post?.date,
      modifiedTime: post?.modified,
      images: [
        {
          url: yoast?.og_image?.[0]?.url,
          width: yoast?.og_image?.[0]?.width,
          height: yoast?.og_image?.[0]?.height,
          alt: yoast?.og_image?.[0]?.alt || post?.title?.rendered,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: yoast?.twitter_title,
      description: yoast?.twitter_description,
      images: [yoast?.twitter_image],
    },
    alternates: {
      canonical: `https://skidos.com/news/${newsID}`,
    },
  };
}

const NotFound = () => {
  const t = useTranslations("News");
  return (
    <div>
      <h1>{t("postNotFound")}</h1>
    </div>
  );
};

const NewsStructuredData = ({ post }) => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "NewsArticle",
    headline: post?.title?.rendered,
    description: post?.excerpt?.rendered,
    image: post?.yoast_head_json?.og_image?.[0]?.url,
    datePublished: post?.date,
    dateModified: post?.modified,
    author: {
      "@type": "Organization",
      name: "SKIDOS",
      url: "https://skidos.com",
    },
    publisher: {
      "@type": "Organization",
      name: "SKIDOS",
      logo: {
        "@type": "ImageObject",
        url: "https://skidos.com/logo.png",
      },
    },
    mainEntityOfPage: {
      "@type": "WebPage",
      "@id": `https://skidos.com/news/${post?.slug}`,
    },
  };

  return <script type="application/ld+json">{JSON.stringify(structuredData)}</script>;
};

const BlogPage = async ({ params }) => {
  const { newsID } = params;

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL}/wp-json/wp/v2/news?slug=${newsID}`,
    {
      next: { revalidate: 3600 },
    }
  );

  if (!response.ok) {
    return <NotFound />;
  }

  const postData = await response.json();
  const post = postData[0];

  return (
    <>
      <NewsStructuredData post={post} />
      <ArticleDetails params={params} />
    </>
  );
};

export default BlogPage;
