"use client";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import Career1 from "../../../public/images/career/Career1.webp";
import Career2 from "../../../public/images/career/Career2.webp";
import Career3 from "../../../public/images/career/Career3.webp";
import Career4 from "../../../public/images/career/Career4.webp";
import Career5 from "../../../public/images/career/Career5.webp";
import Career6 from "../../../public/images/career/Career6.webp";

import "swiper/css";
import "swiper/css/pagination";

import styles from "./styles.module.css";
import "./carousel.css";
import { Pagination, Autoplay } from "swiper/modules";

export default function App() {
  return (
    <>
      <Swiper
        spaceBetween={1}
        pagination={{
          clickable: true,
          el: ".custom-pagination",
        }}
        autoplay={{
          delay: 3000,
          disableOnInteraction: false,
        }}
        modules={[Autoplay, Pagination]}
      >
        <SwiperSlide>
          <div className={styles.carouselContent}>
            <Image src={Career1} layout="responsive" alt="Career 1" />
          </div>
        </SwiperSlide>
        <SwiperSlide>
          <div className={styles.carouselContent}>
            <Image src={Career2} layout="responsive" alt="Career 2" />
          </div>
        </SwiperSlide>
        <SwiperSlide>
          <div className={styles.carouselContent}>
            <Image src={Career3} layout="responsive" alt="Career 3" />
          </div>
        </SwiperSlide>
        <SwiperSlide>
          <div className={styles.carouselContent}>
            <Image src={Career4} layout="responsive" alt="Career 4" />
          </div>
        </SwiperSlide>
        <SwiperSlide>
          <div className={styles.carouselContent}>
            <Image src={Career5} layout="responsive" alt="Career 5" />
          </div>
        </SwiperSlide>
        <SwiperSlide>
          <div className={styles.carouselContent}>
            <Image src={Career6} layout="responsive" alt="Career 6" />
          </div>
        </SwiperSlide>
      </Swiper>
      <div className="parent-container">
        <div className="custom-pagination"></div>
      </div>
    </>
  );
}
