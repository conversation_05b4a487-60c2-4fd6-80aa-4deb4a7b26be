.productsPageWrapper {
  /* max-width: 1512px; */
  max-width: 1728px;
  margin: 0 auto;
}
.productFilterGamesWrapper {
  width: 85%;
  margin: 0 auto;
}
.filterParameterWrapper {
  display: flex;
  gap: 1rem;
  overflow-x: auto;
  scrollbar-width: none;
  -webkit-scrollbar-width: none;
}
.filterParameterWrapper p {
  border-radius: 40px;
  border: 1px solid rgba(210, 210, 210, 1);
  color: rgba(115, 115, 115, 1);
  font-family: var(--font-poppins);
  padding: 8px 16px 8px 16px;
  white-space: nowrap;
}
.filterParameterWrapper p:hover,
.activeFilter {
  background-color: rgba(252, 136, 0, 1);
  color: #ffff !important;
  cursor: pointer;
  border: 1px solid transparent;
}

.gameCardsWrapper {
  display: grid;
  justify-items: center;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
  font-family: var(--font-poppins);
  margin-bottom: 6rem;
}
.gameName {
  font-weight: 700;
  font-size: 1.5rem;
  margin: 0;
}
.gameDescription {
  font-weight: 400;
  font-size: 1.1rem;
  color: rgba(0, 0, 0, 0.5);
  margin: 0;
}
.gameSkillsWrapper {
  display: flex;
  gap: 5px;
}
.gameSkillsWrapper p {
  font-size: 0.9rem;
  background-color: #f7f7f7;
  padding: 0.5rem 1rem 0.5rem 1rem;
  border-radius: 2.5rem;
  border: 1px solid #e9e9e9;
}

.productCards {
  padding: 1rem;
  border-radius: 1rem;
  border: 1px solid rgba(217, 217, 217, 1);
  background-color: #ffffff;
  max-width: 350px;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.15);
  /* transition: box-shadow 0.3s ease-in-out, transform 0.3s ease-in-out;  */
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.productCards:hover {
  box-shadow: 0 0 12px rgba(0, 0, 0, 0);
  transform: scale(0.98);
}
.gameCardsImg {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 20px;
}

.gameCardBtn {
  width: 100%;
  margin: 1rem 0;
  background-color: transparent;
  font-size: 1.2rem;
  color: black;
  border: 1px solid #b0b0b0;
  border-radius: 0.5rem;
  padding: 0.8rem 2rem;
  text-decoration: none;
  cursor: pointer;
}
.gameCardBtn:hover {
  color: #ffff;
  background-color: black;
  border: 1px solid black;
}
@media (max-width: 1184px) {
  .productFilterGamesWrapper {
    width: 95%;
  }
}

@media (max-width: 767px) {
  .gameCardsWrapper {
    grid-template-columns: 1fr;
  }
}
