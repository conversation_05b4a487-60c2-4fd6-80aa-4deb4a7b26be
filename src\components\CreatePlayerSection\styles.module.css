.createPlayerWrapper {
  max-width: 480px;
  margin: 0 auto;
  text-align: center;
  /* border: 2px solid red; */
}
.createPlayerHeader {
  padding: 0.25rem 1rem 0.25rem 1rem;
  text-align: left;
}
.createPlayerWrapper h1 {
  margin: 0;
  font-size: 2rem;
  position: relative;
}
.ctaContainer {
  position: sticky;
  border-top-left-radius: 2rem;
  border-top-right-radius: 2rem;
  bottom: 0px;
  border: 1px solid #dbdbdb;
  background-color: #ffffff;
}
.createPlayerHeader p {
  margin: 0;
  font-family: var(--font-poppins);
  color: #6c757d;
  font-family: 1.2rem;
}
.nameInputWrapper,
.avatarInputWrapper,
.ageInputWrapper {
  display: flex;
  flex-direction: column;
  padding: 0.25rem 1rem 0.25rem 1rem;
  /* border: 1px solid #00000040; */
  border-radius: 10px;
  transition:
    border-color 1s ease-in-out,
    opacity 1s ease-in-out;
}
.avatarInputWrapper,
.ageInputWrapper {
  /* opacity: 0.2; 
    pointer-events: none; */
  transition:
    opacity 1s ease-in-out,
    box-shadow 1s ease-in-out;
}
.disabledSections {
  opacity: 0.2;
  pointer-events: none;
}
.showNoBorder {
  border: none !important;
  box-shadow: none !important;
}
.avatarVisible {
  opacity: 1;
  pointer-events: all;
  border: 1px solid #00000040;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  padding-bottom: 1rem;
}
.nameInputWrapper label,
.avatarInputWrapper label,
.ageInputWrapper label {
  text-align: left;
  color: #6c757d;
  font-size: 1.1rem;
  font-family: var(--font-poppins);
  margin-bottom: 0.3rem;
}
.nameInputWrapper input {
  border-radius: 0.8rem;
  /* border: 0.5px solid #6C757D; */
  padding: 1rem;
  font-size: 1.5rem;
}
.nameInputWrapper input:focus {
  outline: none;
}
.nameInputWrapper input::placeholder {
  color: #6c757d;
  font-family: var(--font-poppins);
}
.defaultBorder {
  border: 1.5px solid #00000040;
}

.greenBorder {
  border: 1.5px solid #32c825;
}

.redBorder {
  border: 1.5px solid red;
}
.avatarWrapper {
  display: flex;
  /* border: 2px solid red; */
}
.selectedAvatar {
  width: 35%;
  border: 1x solid green;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  position: relative;
}
.selectedAvatarImg {
  position: absolute;
  bottom: 15px;
  z-index: 2;
  width: 100px !important;
  height: 100px !important;
}
.platformImg {
  position: absolute;
  bottom: 0;
}
.avatarsList {
  width: 65%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow-x: auto;
  scrollbar-width: none;
}
.avatarsListRow1,
.avatarsListRow2 {
  display: flex;
  gap: 10px;
}
.avatarsListRow1 > div,
.avatarsListRow2 > div {
  background-color: #dedede66;
  padding: 0.4rem;
  cursor: pointer;
}
.avatarsListRow1 img,
.avatarsListRow2 img {
  width: 50px !important;
  height: 50px !important;
}
.avatarItemSelected {
  background-color: white !important;
  border: 1px solid #9258fe;
  border-radius: 0.8rem;
}
.ageWrapper {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
}
.ageWrapperRow1 {
  display: flex;
  gap: 10px;
  justify-content: center;
}
.ageWrapperRow2 {
  display: flex;
  gap: 10px;
  justify-content: center;
}
.ageWrapperRow1 > div,
.ageWrapperRow2 > div {
  padding: 0.5rem;
  font-size: 1.3rem;
  border-radius: 1rem;
  color: #9258fe;
  border: 1px solid #cbb1fc;
  cursor: pointer;
  width: 3.5rem;
  height: 1.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.ageWrapperRow1 > div:hover,
.ageWrapperRow2 > div:hover {
  background-color: #cbb1fc;
  cursor: pointer;
}
.infoText {
  font-size: 16px;
  margin-top: 1rem !important;
  margin-bottom: 0;
  color: #6c757d;
  font-family: var(--font-poppins);
  cursor: pointer;
}
.errorWrapper {
  width: 75%;
  border-radius: 5px;
  background-color: #f8d7ce;
  font-family: var(--font-nevermind-light);
  color: #dd4a38;
  margin-top: 0.5rem;
  padding: 0.5rem 0;
  margin: 1rem auto 0 auto;
}
.errorWrapper p {
  font-family: var(--font-nevermind-medium);
  margin: 0;
}
.errorWrapper span {
  color: #0169dd;
}
.selectedAge {
  background-color: #cbb1fc;
}
@media (max-width: 750px) {
  .createPlayerWrapper {
    width: 90%;
    margin: 0 auto;
  }
  .createPlayerWrapper h1 {
    font-size: 2rem;
  }
  .ageWrapperRow1 {
    gap: 7px;
  }
  .ageWrapperRow2 {
    gap: 7px;
  }
  .ageWrapperRow1 > div,
  .ageWrapperRow2 > div {
    padding: 0.6rem;
    font-size: 1.3rem;
    width: 3rem;
    height: 3rem;
  }
}
