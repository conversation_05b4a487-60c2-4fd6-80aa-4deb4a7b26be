import { NextResponse } from "next/server";

export function middleware(request) {
  const url = request.nextUrl;
  const pathname = url.pathname;

  // WordPress-specific paths handling (keep your existing code)
  const wordpressPaths = ["/wp-content", "/wp-includes", "/wp-admin", "/wp-login.php", "/wp-json"];
  if (wordpressPaths.some((path) => pathname.startsWith(path))) {
    const wordpressUrl = new URL(
      `${process.env.NEXT_PUBLIC_WORDPRESS_WEBSITE}${pathname}${url.search}`
    );
    return NextResponse.rewrite(wordpressUrl);
  }

  // Advanced redirect handling for old routes
  // This can catch edge cases that might not be handled by next.config.mjs
  const redirectMap = {
    "/blog/": "/blogs/",
    "/article/": "/news/",
  };

  // Check if the path starts with any of the keys in redirectMap
  for (const [oldPath, newPath] of Object.entries(redirectMap)) {
    if (pathname.startsWith(oldPath)) {
      const newPathname = pathname.replace(oldPath, newPath);
      const newUrl = new URL(newPathname, request.url);
      newUrl.search = url.search;

      // Add a custom header to track redirects for debugging
      const response = NextResponse.redirect(newUrl, 301);
      response.headers.set("X-Redirect-Source", pathname);
      return response;
    }
  }

  // Authentication protection (from your second middleware)
  if (
    ["/profile/", "/user-home-screen/", "/game-player/"].some((path) => pathname.startsWith(path))
  ) {
    const token = request.cookies.get("token")?.value;
    if (!token) {
      return NextResponse.redirect(new URL("/login", request.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    // WordPress paths
    "/wp-content/:path*",
    "/wp-includes/:path*",
    "/wp-admin/:path*",
    "/wp-login.php",
    "/wp-json/:path*",

    // Old content paths that need redirects
    "/blog/:path*",
    "/article/:path*",

    // Protected routes
    "/profile/:path*",
    "/user-home-screen/:path*",
    "/game-player/:path*",
  ],
};
