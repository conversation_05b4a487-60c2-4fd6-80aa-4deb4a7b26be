.contentInput {
  display: flex;
  /* gap:1rem; */
  justify-content: space-between;
}

.formOtpInput {
  width: 90px;
  height: 76.3px;
  box-sizing: border-box;
  border: 2.27px solid rgba(185, 185, 185, 1);
  border-radius: 1.5rem;
  padding: 1.5rem 2.1rem 1.5rem 2.1rem;
  font-size: 1.7rem;
  margin-bottom: 2rem;
  gap: 1.1rem;
}

.formOtpInputError {
  border: 2.27px solid rgba(255, 0, 0, 1);
  background-color: rgba(255, 0, 0, 0.1);
}

@media (max-width: 470px) {
  .formOtpInput {
    width: 50px;
    height: 50px;
    padding: 1rem;
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    gap: 0.5rem;
    text-align: center;
    border-radius: 1rem;
  }

  .contentInput {
    gap: 1.5rem;
  }
}

.formOtpInput:focus {
  outline: none;
}

.formOtpInput::placeholder {
  font-size: 1.7rem;
  color: rgba(185, 185, 185, 1);
}
