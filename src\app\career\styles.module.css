.careerWrapper {
  /* max-width: 1512px; */
  max-width: 1728px;
  margin: 0 auto;
}
.jobsContainer {
  padding: 0 2rem 2rem 2rem;
}

.jobGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.jobCard {
  border: 1px solid #ddd;
  border-radius: 12px;
  padding: 20px 20px 40px 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background-image: url("/images/career/job-bg.png");
  background-size: cover;
  background-position: center;
}

.jobTitle {
  font-family: var(--font-poppins);
  font-size: 2.2rem;
  font-weight: 600;
  line-height: 60px;
  margin: 0;
}

.jobDescription,
.jobDetail {
  font-family: var(--font-poppins);
  font-weight: 400;
  font-size: 1.2rem;
  line-height: 30px;
  color: #747474;
}

.jobDetail {
  margin-bottom: 8px;
  font-family: var(--font-poppins);
}

.icon {
  margin-right: 20px;
}

.applyButton {
  background-color: #000;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  transition:
    background-color 0.3s ease,
    transform 0.3s ease;
  font-family: var(--font-poppins);
  font-size: 20px;
  font-weight: 500;
  line-height: 30px;
  margin-top: 2rem;
}

.applyButton.hovered {
  background-color: #6f42c1;
  transform: translateX(4px);
}

.arrow {
  margin-left: 8px;
  transition: transform 0.3s ease;
}

.comingSoonContainer {
  text-align: center;
  font-family: var(--font-poppins);
  font-size: 1.5rem;
  font-weight: 500;
  line-height: 40px;
  background-color: #eaeaea;
  padding: 3.5rem;
  border-radius: 16px;
  margin: 2rem 4rem;
  color: #595959;
}
.comingSoonContainer h2 {
  font-family: var(--font-nevermind-bold);
  margin-top: 0.5rem;
  color: black;
}
.linkedinCta {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0 auto;
  background-color: #0b63bd;
  border: none;
  color: #ffff;
  font-size: 1.5rem;
  font-weight: 600;
  padding: 1rem 2.5rem;
  cursor: pointer;
  box-shadow: 0px 8.7px 0px 0px #004e76;
  border-radius: 1rem;
}
.eduInnoCont {
  text-align: center;
  margin: 2rem 4rem;
}
.gridContainer {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  width: 100%;
}
.lastRowContianer {
  display: flex;
  gap: 10px;
  margin: 10px 0;
  width: 100%;
}
.lastRowContianer > div {
  width: 50% !important;
}
.box {
  background: #e8dcfffc;
  text-align: center;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
}
.box p {
  font-size: 1.5rem;
  color: #595959;
  font-family: var(--font-poppins);
  font-weight: 500;
  line-height: 33px;
}

@media only screen and (min-width: 600px) and (max-width: 900px) {
  .gridContainer {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 599px) {
  .jobGrid {
    grid-template-columns: 1fr;
  }
  .eduInnoCont {
    margin: 2rem 1.5rem;
  }
  .comingSoonContainer {
    line-height: 30px;
    padding: 2.5rem;
    margin: 2rem 1.5rem;
  }
  .gridContainer {
    grid-template-columns: repeat(1, 1fr);
  }
  .box p {
    font-size: 1.5rem;
    line-height: 23px;
  }
  .lastRowContianer {
    display: flex;
    flex-direction: column;
  }
  .lastRowContianer > div {
    width: 100% !important;
    box-sizing: border-box;
  }
}
