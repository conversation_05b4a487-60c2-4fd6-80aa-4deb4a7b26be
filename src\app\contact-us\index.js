import { useTranslations } from "next-intl";
import Head from "next/head";
import Image from "next/image";
import Mascots from "../../../public/images/contactUs/contactUsMascots.webp";
import styles from "./styles.module.css";

const ContactUs = () => {
  const t = useTranslations("ContactUs");

  return (
    <>
      <Head>
        <title>{t("title")}</title>
        <meta name="description" content={t("metaDescription")} />
      </Head>
      <div className={styles.contactUsWrapper}>
        <header className={styles.contactUsHeader}>
          <div>
            <h2>{t("header")}</h2>
            <div className={styles.addressWrapper}>
              <p>{t("addressLabel")}</p>
              <p>{t("address")}</p>
            </div>
            <div className={styles.addressWrapper}>
              <p>{t("emailLabel")}</p>
              <p>{t("email")}</p>
            </div>
          </div>
          <div>
            <Image alt={t("mascotsAlt")} src={Mascots} className={styles.contactUsMascotsImg} />
          </div>
        </header>
        <section className={styles.contactUsContainer}>
          <h1>{t("formTitle")}</h1>
          <form className={styles.contactUsForm}>
            <div className={styles.nameEmailWrapper}>
              <div className={styles.formGroup}>
                <input
                  placeholder={t("form.firstName")}
                  type="text"
                  id="firstName"
                  name="firstName"
                />
              </div>
              <div className={styles.formGroup}>
                <input placeholder={t("form.email")} type="email" id="email" name="email" />
              </div>
            </div>
            <div className={`${styles.formGroup} ${styles.fullWidth}`}>
              <select id="category" name="category">
                <option value="">{t("form.selectCategory")}</option>
                <option value="general">{t("form.general")}</option>
                <option value="support">{t("form.support")}</option>
                <option value="sales">{t("form.sales")}</option>
              </select>
            </div>
            <div className={`${styles.formGroup} ${styles.fullWidth}`}>
              <textarea
                placeholder={t("form.message")}
                id="message"
                name="message"
                rows="10"
              ></textarea>
            </div>
            <div className={styles.checkboxGroup}>
              <input type="checkbox" id="privacyPolicy" name="privacyPolicy" />
              <label htmlFor="privacyPolicy">
                {t("form.privacyPolicy")} <a href="#">{t("form.privacyLink")}</a>
              </label>
            </div>
            <button type="submit" className={styles.formBtn}>
              {t("form.submit")}
            </button>
          </form>
        </section>
      </div>
    </>
  );
};

export default ContactUs;
