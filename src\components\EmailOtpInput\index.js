"use client";
import React, { useState, useRef } from "react";
import styles from "./styles.module.css";
const EmailOtpInput = ({ onOtpChange, error }) => {
  const [otp, setOtp] = useState(new Array(4).fill(""));
  const inputsRef = useRef([]);

  const handleChange = (element, index) => {
    if (isNaN(element.value)) return;

    let newOtp = [...otp];
    newOtp[index] = element.value;
    setOtp(newOtp);

    if (element.value !== "" && index < 5) {
      inputsRef?.current[index + 1]?.focus();
    }
    onOtpChange(newOtp.join(""));
  };

  const handleKeyDown = (event, index) => {
    if (event.key === "Backspace") {
      if (otp[index] === "" && index > 0) {
        inputsRef?.current[index - 1]?.focus();
      }
    }
  };

  const handlePaste = (event) => {
    const pastedData = event.clipboardData.getData("text").slice(0, 6).split("");

    setOtp(pastedData);
    onOtpChange(pastedData.join(""));
    pastedData.forEach((value, index) => {
      if (inputsRef?.current[index]) {
        inputsRef.current[index].value = value;
        if (index < 3) {
          inputsRef?.current[index + 1]?.focus();
        }
      }
    });
  };

  return (
    <div className={styles.contentInput}>
      {otp.map((value, index) => (
        <input
          key={index}
          type="text"
          maxLength="1"
          value={value}
          onChange={(e) => handleChange(e.target, index)}
          onKeyDown={(e) => handleKeyDown(e, index)}
          onPaste={handlePaste}
          ref={(el) => (inputsRef.current[index] = el)}
          className={`${styles.formOtpInput} ${error ? styles.formOtpInputError : ""}`}
        />
      ))}
    </div>
  );
};

export default EmailOtpInput;
